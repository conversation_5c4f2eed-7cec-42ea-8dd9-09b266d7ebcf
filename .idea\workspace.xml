<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="92aeed09-4029-48eb-8347-0f1a698d2b7b" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="31i12CbuDr6ISK3XTEU5R0Q7c6s" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="SkinsTools package" type="MavenRunConfiguration" factoryName="Maven">
      <MavenSettings>
        <option name="myGeneralSettings" />
        <option name="myRunnerSettings" />
        <option name="myRunnerParameters">
          <MavenRunnerParameters>
            <option name="cmdOptions" />
            <option name="profiles">
              <set />
            </option>
            <option name="goals">
              <list>
                <option value="package" />
              </list>
            </option>
            <option name="multimoduleDir" />
            <option name="pomFileName" />
            <option name="profilesMap">
              <map />
            </option>
            <option name="projectsCmdOptionValues">
              <list />
            </option>
            <option name="resolveToWorkspace" value="false" />
            <option name="workingDirPath" value="D:\MinecraftPlugins\SkinsTools" />
          </MavenRunnerParameters>
        </option>
      </MavenSettings>
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="92aeed09-4029-48eb-8347-0f1a698d2b7b" name="更改" comment="" />
      <created>1755991871793</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755991871793</updated>
    </task>
    <servers />
  </component>
</project>