package org.plugin.skinstools;

import org.bukkit.configuration.file.YamlConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.plugin.skinstools.config.ConfigManager;

import java.io.StringReader;
import java.util.logging.Logger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ConfigManager测试类
 * ConfigManager Test Class
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ConfigManagerTest {
    
    @Mock
    private SkinsTools plugin;
    
    @Mock
    private Logger logger;
    
    private ConfigManager configManager;
    
    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        when(plugin.getPluginLogger()).thenReturn(logger);
        
        // 创建测试配置
        String testConfig = """
            blessing-skin:
              url: "https://test.example.com"
              timeout: 3000
              retry-attempts: 2
              user-agent: "TestAgent/1.0"
            
            cache:
              enabled: true
              memory-size: 500
              expiration-hours: 12
              database:
                enabled: true
                file: "test.db"
                pool:
                  maximum-pool-size: 5
                  minimum-idle: 1
                  connection-timeout: 15000
            
            skin-processing:
              auto-apply: false
              join-delay: 10
              default-skin: "Alex"
              update-mode: "IMMEDIATE"
            
            logging:
              debug: true
              log-requests: true
              log-cache: true
            
            performance:
              async: false
              thread-pool-size: 2
              batch-size: 5
            
            compatibility:
              check-other-plugins: false
              supported-formats:
                - "png"
                - "jpg"
            
            messages:
              prefix: "&a[Test] &r"
              skin-updated: "&aTest skin updated!"
              skin-not-found: "&cTest skin not found!"
            """;
        
        YamlConfiguration config = YamlConfiguration.loadConfiguration(new StringReader(testConfig));
        when(plugin.getConfig()).thenReturn(config);

        configManager = new ConfigManager(plugin);
        // 手动设置配置，因为测试环境中无法调用loadConfig
        java.lang.reflect.Field configField = ConfigManager.class.getDeclaredField("config");
        configField.setAccessible(true);
        configField.set(configManager, config);
    }
    
    @Test
    void testBlessingSkinConfiguration() {
        assertEquals("https://test.example.com", configManager.getBlessingSkinUrl());
        assertEquals(3000, configManager.getApiTimeout());
        assertEquals(2, configManager.getRetryAttempts());
        assertEquals("TestAgent/1.0", configManager.getUserAgent());
    }
    
    @Test
    void testCacheConfiguration() {
        assertTrue(configManager.isCacheEnabled());
        assertEquals(500, configManager.getMemoryCacheSize());
        assertEquals(12, configManager.getCacheExpirationHours());
        assertTrue(configManager.isDatabaseCacheEnabled());
        assertEquals("test.db", configManager.getDatabaseFile());
        assertEquals(5, configManager.getMaximumPoolSize());
        assertEquals(1, configManager.getMinimumIdle());
        assertEquals(15000, configManager.getConnectionTimeout());
    }
    
    @Test
    void testSkinProcessingConfiguration() {
        assertFalse(configManager.isAutoApply());
        assertEquals(10, configManager.getJoinDelay());
        assertEquals("Alex", configManager.getDefaultSkin());
        assertEquals("IMMEDIATE", configManager.getUpdateMode());
    }
    
    @Test
    void testLoggingConfiguration() {
        assertTrue(configManager.isDebugEnabled());
        assertTrue(configManager.isLogRequestsEnabled());
        assertTrue(configManager.isLogCacheEnabled());
    }
    
    @Test
    void testPerformanceConfiguration() {
        assertFalse(configManager.isAsyncEnabled());
        assertEquals(2, configManager.getThreadPoolSize());
        assertEquals(5, configManager.getBatchSize());
    }
    
    @Test
    void testCompatibilityConfiguration() {
        assertFalse(configManager.isCheckOtherPlugins());
        assertEquals(2, configManager.getSupportedFormats().size());
        assertTrue(configManager.getSupportedFormats().contains("png"));
        assertTrue(configManager.getSupportedFormats().contains("jpg"));
    }
    
    @Test
    void testMessagesConfiguration() {
        assertEquals("&a[Test] &r", configManager.getPrefix());
        assertEquals("&aTest skin updated!", configManager.getMessage("skin-updated"));
        assertEquals("&cTest skin not found!", configManager.getMessage("skin-not-found"));
    }
    
    @Test
    void testDefaultValues() throws Exception {
        // 测试不存在的配置项是否返回默认值
        YamlConfiguration emptyConfig = new YamlConfiguration();
        when(plugin.getConfig()).thenReturn(emptyConfig);

        ConfigManager emptyConfigManager = new ConfigManager(plugin);
        // 手动设置空配置
        java.lang.reflect.Field configField = ConfigManager.class.getDeclaredField("config");
        configField.setAccessible(true);
        configField.set(emptyConfigManager, emptyConfig);

        assertEquals("https://skin.example.com", emptyConfigManager.getBlessingSkinUrl());
        assertEquals(5000, emptyConfigManager.getApiTimeout());
        assertEquals(3, emptyConfigManager.getRetryAttempts());
        assertTrue(emptyConfigManager.isCacheEnabled());
        assertEquals(1000, emptyConfigManager.getMemoryCacheSize());
        assertEquals(24, emptyConfigManager.getCacheExpirationHours());
    }
    
    @Test
    void testCacheExpirationValidation() throws Exception {
        // 测试缓存过期时间验证（最小值为1小时）
        YamlConfiguration invalidConfig = new YamlConfiguration();
        invalidConfig.set("cache.expiration-hours", 0);
        when(plugin.getConfig()).thenReturn(invalidConfig);

        ConfigManager invalidConfigManager = new ConfigManager(plugin);
        // 手动设置无效配置
        java.lang.reflect.Field configField = ConfigManager.class.getDeclaredField("config");
        configField.setAccessible(true);
        configField.set(invalidConfigManager, invalidConfig);

        assertEquals(1, invalidConfigManager.getCacheExpirationHours());
    }
}
