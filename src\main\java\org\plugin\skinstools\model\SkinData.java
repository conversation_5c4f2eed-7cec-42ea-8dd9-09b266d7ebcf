package org.plugin.skinstools.model;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.Instant;
import java.util.Objects;
import java.util.UUID;

/**
 * 皮肤数据模型
 * Skin Data Model
 * 
 * 存储皮肤的所有相关信息，包括纹理数据、签名和元数据
 * Stores all skin-related information including texture data, signature and metadata
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SkinData {
    
    private final UUID playerId;
    private final String playerName;
    private final String textureValue;
    private final String textureSignature;
    private final String skinUrl;
    private final String capeUrl;
    private final Instant createdAt;
    private final Instant updatedAt;
    private final boolean isSlim;
    
    /**
     * 构造函数
     * Constructor
     * 
     * @param playerId 玩家UUID Player UUID
     * @param playerName 玩家名称 Player name
     * @param textureValue Base64编码的纹理数据 Base64 encoded texture data
     * @param textureSignature 纹理签名 Texture signature
     * @param skinUrl 皮肤URL Skin URL
     * @param capeUrl 披风URL Cape URL
     * @param isSlim 是否为纤细模型 Whether it's a slim model
     */
    public SkinData(@NotNull UUID playerId,
                    @NotNull String playerName,
                    @NotNull String textureValue,
                    @Nullable String textureSignature,
                    @Nullable String skinUrl,
                    @Nullable String capeUrl,
                    boolean isSlim) {
        this.playerId = Objects.requireNonNull(playerId, "Player ID cannot be null");
        this.playerName = Objects.requireNonNull(playerName, "Player name cannot be null");
        this.textureValue = Objects.requireNonNull(textureValue, "Texture value cannot be null");
        this.textureSignature = textureSignature;
        this.skinUrl = skinUrl;
        this.capeUrl = capeUrl;
        this.isSlim = isSlim;
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }
    
    /**
     * 用于从数据库加载的构造函数
     * Constructor for loading from database
     */
    public SkinData(@NotNull UUID playerId,
                    @NotNull String playerName,
                    @NotNull String textureValue,
                    @Nullable String textureSignature,
                    @Nullable String skinUrl,
                    @Nullable String capeUrl,
                    boolean isSlim,
                    @NotNull Instant createdAt,
                    @NotNull Instant updatedAt) {
        this.playerId = Objects.requireNonNull(playerId, "Player ID cannot be null");
        this.playerName = Objects.requireNonNull(playerName, "Player name cannot be null");
        this.textureValue = Objects.requireNonNull(textureValue, "Texture value cannot be null");
        this.textureSignature = textureSignature;
        this.skinUrl = skinUrl;
        this.capeUrl = capeUrl;
        this.isSlim = isSlim;
        this.createdAt = Objects.requireNonNull(createdAt, "Created at cannot be null");
        this.updatedAt = Objects.requireNonNull(updatedAt, "Updated at cannot be null");
    }
    
    /**
     * 获取玩家UUID
     * Get player UUID
     * 
     * @return 玩家UUID Player UUID
     */
    @NotNull
    public UUID getPlayerId() {
        return playerId;
    }
    
    /**
     * 获取玩家名称
     * Get player name
     * 
     * @return 玩家名称 Player name
     */
    @NotNull
    public String getPlayerName() {
        return playerName;
    }
    
    /**
     * 获取Base64编码的纹理数据
     * Get Base64 encoded texture data
     * 
     * @return 纹理数据 Texture data
     */
    @NotNull
    public String getTextureValue() {
        return textureValue;
    }
    
    /**
     * 获取纹理签名
     * Get texture signature
     * 
     * @return 纹理签名 Texture signature
     */
    @Nullable
    public String getTextureSignature() {
        return textureSignature;
    }
    
    /**
     * 获取皮肤URL
     * Get skin URL
     * 
     * @return 皮肤URL Skin URL
     */
    @Nullable
    public String getSkinUrl() {
        return skinUrl;
    }
    
    /**
     * 获取披风URL
     * Get cape URL
     * 
     * @return 披风URL Cape URL
     */
    @Nullable
    public String getCapeUrl() {
        return capeUrl;
    }
    
    /**
     * 是否为纤细模型
     * Whether it's a slim model
     * 
     * @return 是否为纤细模型 Whether it's a slim model
     */
    public boolean isSlim() {
        return isSlim;
    }
    
    /**
     * 获取创建时间
     * Get creation time
     * 
     * @return 创建时间 Creation time
     */
    @NotNull
    public Instant getCreatedAt() {
        return createdAt;
    }
    
    /**
     * 获取更新时间
     * Get update time
     * 
     * @return 更新时间 Update time
     */
    @NotNull
    public Instant getUpdatedAt() {
        return updatedAt;
    }
    
    /**
     * 检查皮肤数据是否有效
     * Check if skin data is valid
     * 
     * @return 是否有效 Whether it's valid
     */
    public boolean isValid() {
        return textureValue != null && !textureValue.trim().isEmpty();
    }
    
    /**
     * 检查是否有签名
     * Check if has signature
     * 
     * @return 是否有签名 Whether has signature
     */
    public boolean hasSignature() {
        return textureSignature != null && !textureSignature.trim().isEmpty();
    }
    
    /**
     * 创建更新的副本
     * Create updated copy
     * 
     * @return 更新的皮肤数据 Updated skin data
     */
    @NotNull
    public SkinData withUpdatedTime() {
        return new SkinData(playerId, playerName, textureValue, textureSignature,
                           skinUrl, capeUrl, isSlim, createdAt, Instant.now());
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SkinData skinData = (SkinData) o;
        return isSlim == skinData.isSlim &&
               Objects.equals(playerId, skinData.playerId) &&
               Objects.equals(playerName, skinData.playerName) &&
               Objects.equals(textureValue, skinData.textureValue) &&
               Objects.equals(textureSignature, skinData.textureSignature);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(playerId, playerName, textureValue, textureSignature, isSlim);
    }
    
    @Override
    public String toString() {
        return "SkinData{" +
               "playerId=" + playerId +
               ", playerName='" + playerName + '\'' +
               ", hasTexture=" + (textureValue != null) +
               ", hasSignature=" + hasSignature() +
               ", isSlim=" + isSlim +
               ", createdAt=" + createdAt +
               ", updatedAt=" + updatedAt +
               '}';
    }
}
