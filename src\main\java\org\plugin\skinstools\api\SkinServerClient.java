package org.plugin.skinstools.api;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.plugin.skinstools.config.ConfigManager;
import org.plugin.skinstools.model.SkinData;

import java.io.IOException;
import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * Blessing Skin Server API客户端
 * Blessing Skin Server API Client
 * 
 * 负责与Blessing Skin Server进行通信，获取皮肤数据
 * Responsible for communicating with Blessing Skin Server to retrieve skin data
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SkinServerClient {
    
    private final ConfigManager configManager;
    private final Logger logger;
    private final OkHttpClient httpClient;

    public SkinServerClient(@NotNull ConfigManager configManager, @NotNull Logger logger) {
        this.configManager = configManager;
        this.logger = logger;
        
        // 配置HTTP客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(configManager.getApiTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(configManager.getApiTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(configManager.getApiTimeout(), TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 异步根据UUID获取皮肤数据
     * Asynchronously get skin data by UUID
     * 
     * @param uuid 玩家UUID Player UUID
     * @return 皮肤数据的CompletableFuture CompletableFuture of skin data
     */
    @NotNull
    public CompletableFuture<SkinData> getSkinByUUID(@NotNull UUID uuid) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getSkinByUUIDSync(uuid);
            } catch (Exception e) {
                logger.warning("获取皮肤失败 (UUID: " + uuid + "): " + e.getMessage());
                logger.warning("Failed to get skin (UUID: " + uuid + "): " + e.getMessage());
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 异步根据用户名获取皮肤数据
     * Asynchronously get skin data by username
     * 
     * @param username 用户名 Username
     * @return 皮肤数据的CompletableFuture CompletableFuture of skin data
     */
    @NotNull
    public CompletableFuture<SkinData> getSkinByUsername(@NotNull String username) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getSkinByUsernameSync(username);
            } catch (Exception e) {
                logger.warning("获取皮肤失败 (用户名: " + username + "): " + e.getMessage());
                logger.warning("Failed to get skin (Username: " + username + "): " + e.getMessage());
                throw new RuntimeException(e);
            }
        });
    }
    
    /**
     * 同步根据UUID获取皮肤数据
     * Synchronously get skin data by UUID
     * 
     * @param uuid 玩家UUID Player UUID
     * @return 皮肤数据 Skin data
     * @throws IOException 网络异常 Network exception
     */
    @Nullable
    public SkinData getSkinByUUIDSync(@NotNull UUID uuid) throws IOException {
        String url = configManager.getBlessingSkinUrl() + "/api/profiles/minecraft/" + uuid.toString().replace("-", "");
        return fetchSkinData(url, uuid.toString());
    }
    
    /**
     * 同步根据用户名获取皮肤数据
     * Synchronously get skin data by username
     * 
     * @param username 用户名 Username
     * @return 皮肤数据 Skin data
     * @throws IOException 网络异常 Network exception
     */
    @Nullable
    public SkinData getSkinByUsernameSync(@NotNull String username) throws IOException {
        // 首先通过用户名获取UUID
        String profileUrl = configManager.getBlessingSkinUrl() + "/api/profiles/minecraft/" + username;
        
        Request profileRequest = new Request.Builder()
                .url(profileUrl)
                .header("User-Agent", configManager.getUserAgent())
                .build();
        
        try (Response response = executeWithRetry(profileRequest)) {
            if (!response.isSuccessful()) {
                if (response.code() == 404) {
                    logger.info("用户不存在: " + username);
                    logger.info("User not found: " + username);
                    return null;
                }
                throw new IOException("HTTP " + response.code() + ": " + response.message());
            }
            
            String responseBody = response.body().string();
            JsonObject profileJson = JsonParser.parseString(responseBody).getAsJsonObject();
            
            if (!profileJson.has("id")) {
                logger.warning("响应中缺少UUID字段");
                logger.warning("Missing UUID field in response");
                return null;
            }
            
            String uuidString = profileJson.get("id").getAsString();
            UUID uuid = UUID.fromString(
                uuidString.replaceFirst(
                    "(\\p{XDigit}{8})(\\p{XDigit}{4})(\\p{XDigit}{4})(\\p{XDigit}{4})(\\p{XDigit}+)",
                    "$1-$2-$3-$4-$5"
                )
            );
            
            // 然后获取皮肤数据
            return getSkinByUUIDSync(uuid);
        }
    }
    
    /**
     * 获取皮肤数据
     * Fetch skin data
     * 
     * @param url API URL
     * @param identifier 标识符 Identifier
     * @return 皮肤数据 Skin data
     * @throws IOException 网络异常 Network exception
     */
    @Nullable
    private SkinData fetchSkinData(@NotNull String url, @NotNull String identifier) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .header("User-Agent", configManager.getUserAgent())
                .build();
        
        if (configManager.isLogRequestsEnabled()) {
            logger.info("发送API请求: " + url);
            logger.info("Sending API request: " + url);
        }
        
        try (Response response = executeWithRetry(request)) {
            if (!response.isSuccessful()) {
                if (response.code() == 404) {
                    logger.info("皮肤不存在: " + identifier);
                    logger.info("Skin not found: " + identifier);
                    return null;
                }
                throw new IOException("HTTP " + response.code() + ": " + response.message());
            }
            
            String responseBody = response.body().string();
            
            if (configManager.isLogRequestsEnabled()) {
                logger.info("API响应: " + responseBody);
                logger.info("API response: " + responseBody);
            }
            
            return parseSkinResponse(responseBody);
        }
    }
    
    /**
     * 解析皮肤响应
     * Parse skin response
     * 
     * @param responseBody 响应体 Response body
     * @return 皮肤数据 Skin data
     */
    @Nullable
    private SkinData parseSkinResponse(@NotNull String responseBody) {
        try {
            JsonObject json = JsonParser.parseString(responseBody).getAsJsonObject();
            
            if (!json.has("properties") || json.getAsJsonArray("properties").size() == 0) {
                logger.warning("响应中缺少properties字段");
                logger.warning("Missing properties field in response");
                return null;
            }
            
            JsonObject properties = json.getAsJsonArray("properties").get(0).getAsJsonObject();
            String textureValue = properties.get("value").getAsString();
            String textureSignature = properties.has("signature") ? 
                properties.get("signature").getAsString() : null;
            
            String playerName = json.has("name") ? json.get("name").getAsString() : "Unknown";
            String uuidString = json.get("id").getAsString();
            UUID uuid = UUID.fromString(
                uuidString.replaceFirst(
                    "(\\p{XDigit}{8})(\\p{XDigit}{4})(\\p{XDigit}{4})(\\p{XDigit}{4})(\\p{XDigit}+)",
                    "$1-$2-$3-$4-$5"
                )
            );
            
            // 解析纹理数据以获取URL和模型信息
            String decodedTexture = new String(java.util.Base64.getDecoder().decode(textureValue));
            JsonObject textureJson = JsonParser.parseString(decodedTexture).getAsJsonObject();
            
            String skinUrl = null;
            String capeUrl = null;
            boolean isSlim = false;
            
            if (textureJson.has("textures")) {
                JsonObject textures = textureJson.getAsJsonObject("textures");
                
                if (textures.has("SKIN")) {
                    JsonObject skin = textures.getAsJsonObject("SKIN");
                    skinUrl = skin.get("url").getAsString();
                    
                    if (skin.has("metadata")) {
                        JsonObject metadata = skin.getAsJsonObject("metadata");
                        isSlim = "slim".equals(metadata.get("model").getAsString());
                    }
                }
                
                if (textures.has("CAPE")) {
                    JsonObject cape = textures.getAsJsonObject("CAPE");
                    capeUrl = cape.get("url").getAsString();
                }
            }
            
            return new SkinData(uuid, playerName, textureValue, textureSignature, 
                              skinUrl, capeUrl, isSlim);
            
        } catch (Exception e) {
            logger.warning("解析皮肤响应失败: " + e.getMessage());
            logger.warning("Failed to parse skin response: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 带重试的请求执行
     * Execute request with retry
     * 
     * @param request 请求 Request
     * @return 响应 Response
     * @throws IOException 网络异常 Network exception
     */
    @NotNull
    private Response executeWithRetry(@NotNull Request request) throws IOException {
        IOException lastException = null;
        
        for (int i = 0; i < configManager.getRetryAttempts(); i++) {
            try {
                return httpClient.newCall(request).execute();
            } catch (IOException e) {
                lastException = e;
                if (i < configManager.getRetryAttempts() - 1) {
                    logger.warning("请求失败，正在重试 (" + (i + 1) + "/" + configManager.getRetryAttempts() + "): " + e.getMessage());
                    logger.warning("Request failed, retrying (" + (i + 1) + "/" + configManager.getRetryAttempts() + "): " + e.getMessage());
                    
                    try {
                        Thread.sleep(1000 * (i + 1)); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Interrupted during retry", ie);
                    }
                }
            }
        }

        throw lastException != null ? lastException : new IOException("All retry attempts failed");
    }
    
    /**
     * 关闭客户端
     * Close client
     */
    public void close() {
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
    }
}
