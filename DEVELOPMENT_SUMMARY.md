# SkinsTools 开发总结

## 项目概述

SkinsTools 是一个为 Minecraft Paper 1.20.1 服务器开发的皮肤插件，旨在解决玩家因网络问题无法正常显示皮肤的问题。通过本地缓存和 Blessing Skin Server 集成，为玩家提供稳定的皮肤服务。

## 核心功能

### 1. 皮肤缓存系统
- **多层缓存架构**：内存缓存（Caffeine）+ 数据库缓存（SQLite）
- **智能过期管理**：自动清理过期缓存，支持手动管理
- **高性能设计**：异步处理，不阻塞主线程

### 2. Blessing Skin Server 集成
- **完整 API 支持**：支持通过 UUID 和用户名获取皮肤
- **错误处理机制**：重试机制、超时处理、异常恢复
- **兼容性保证**：支持标准 Blessing Skin Server API

### 3. 皮肤处理引擎
- **GameProfile 操作**：正确处理 Paper API 的皮肤属性
- **纹理格式支持**：Base64 编码、签名验证
- **实时更新**：支持玩家皮肤的实时刷新和应用

### 4. 命令系统
- **管理命令**：`/skinstools` - 配置管理、缓存操作、系统信息
- **用户命令**：`/skin` - 皮肤设置和刷新
- **权限控制**：细粒度权限管理

## 技术架构

### 依赖管理
```xml
- Paper API 1.20.1
- OkHttp 4.12.0 (HTTP 客户端)
- Gson 2.10.1 (JSON 处理)
- SQLite JDBC 3.44.1.0 (数据库)
- HikariCP 5.1.0 (连接池)
- Caffeine 3.1.8 (内存缓存)
```

### 包结构
```
org.plugin.skinstools/
├── api/           # API 客户端
├── cache/         # 缓存系统
├── command/       # 命令处理
├── config/        # 配置管理
├── database/      # 数据库操作
├── listener/      # 事件监听
├── model/         # 数据模型
└── processor/     # 皮肤处理
```

### 设计模式
- **单例模式**：插件主类实例管理
- **工厂模式**：组件创建和初始化
- **观察者模式**：事件监听和处理
- **策略模式**：不同的皮肤更新策略

## 关键实现细节

### 1. 皮肤数据模型
```java
public class SkinData {
    private final UUID playerId;
    private final String playerName;
    private final String textureValue;    // Base64 编码的纹理数据
    private final String textureSignature; // 纹理签名
    private final String skinUrl;         // 皮肤 URL
    private final String capeUrl;         // 披风 URL
    private final boolean isSlim;         // 是否为纤细模型
    // ... 时间戳和其他元数据
}
```

### 2. 缓存策略
- **L1 缓存**：Caffeine 内存缓存，快速访问
- **L2 缓存**：SQLite 数据库缓存，持久化存储
- **缓存一致性**：写入时同时更新两级缓存
- **过期策略**：基于时间的自动过期 + 手动清理

### 3. 异步处理
```java
// 异步皮肤处理
CompletableFuture<Boolean> processPlayerSkinAsync(Player player);

// 异步缓存操作
CompletableFuture<Void> cacheSkinDataAsync(SkinData skinData);

// 异步 API 请求
CompletableFuture<SkinData> getSkinByUUID(UUID uuid);
```

### 4. 错误处理
- **重试机制**：API 请求失败时自动重试
- **降级策略**：网络失败时使用缓存数据
- **日志记录**：详细的错误日志和调试信息
- **异常恢复**：优雅处理各种异常情况

## 配置系统

### 主要配置项
```yaml
blessing-skin:
  url: "https://your-skin-server.com"
  timeout: 5000
  retry-attempts: 3

cache:
  enabled: true
  memory-size: 1000
  expiration-hours: 24

skin-processing:
  auto-apply: true
  join-delay: 20
  update-mode: "DELAYED"
```

### 更新模式
- **IMMEDIATE**：立即更新皮肤
- **DELAYED**：延迟更新（推荐）
- **NEXT_JOIN**：下次加入时更新

## 测试覆盖

### 单元测试
- **ConfigManager 测试**：配置加载和验证
- **模拟环境**：使用 Mockito 模拟 Bukkit API
- **边界测试**：测试各种边界条件和异常情况

### 测试结果
```
Tests run: 9, Failures: 0, Errors: 0, Skipped: 0
```

## 性能优化

### 1. 异步处理
- 所有网络请求都在异步线程中执行
- 皮肤应用操作不阻塞主线程
- 缓存操作异步化

### 2. 连接池管理
- HikariCP 数据库连接池
- OkHttp 连接复用
- 资源自动回收

### 3. 内存优化
- Caffeine 缓存自动过期
- 弱引用和软引用使用
- 定期清理过期数据

## 部署和使用

### 安装步骤
1. 下载 `SkinsTools-1.0.0.jar`
2. 放入服务器 `plugins` 文件夹
3. 重启服务器
4. 配置 `config.yml`
5. 重载插件配置

### 基本使用
```bash
# 管理命令
/skinstools reload          # 重载配置
/skinstools cache stats     # 查看缓存统计
/skinstools refresh player  # 刷新玩家皮肤

# 用户命令
/skin                       # 刷新自己的皮肤
/skin Steve                 # 设置皮肤为 Steve
```

## 未来扩展

### 计划功能
1. **多皮肤站支持**：支持多个皮肤站的负载均衡
2. **皮肤预览**：在设置前预览皮肤效果
3. **批量操作**：批量刷新多个玩家的皮肤
4. **统计功能**：皮肤使用统计和分析
5. **Web 界面**：基于 Web 的管理界面

### 技术改进
1. **Redis 支持**：支持 Redis 作为分布式缓存
2. **CDN 集成**：支持 CDN 加速皮肤下载
3. **监控集成**：集成 Prometheus 监控
4. **配置热更新**：支持配置的热更新

## 总结

SkinsTools 插件成功实现了以下目标：

1. **解决核心问题**：有效解决了玩家皮肤显示问题
2. **高性能设计**：异步处理和多层缓存确保高性能
3. **稳定可靠**：完善的错误处理和测试覆盖
4. **易于使用**：简洁的命令系统和配置选项
5. **可扩展性**：模块化设计便于未来扩展

该插件为 Minecraft 服务器提供了一个完整的皮肤管理解决方案，特别适合需要与 Blessing Skin Server 集成的服务器环境。
