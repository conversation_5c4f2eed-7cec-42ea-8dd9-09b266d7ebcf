package org.plugin.skinstools.listener;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.jetbrains.annotations.NotNull;
import org.plugin.skinstools.SkinsTools;
import org.plugin.skinstools.config.ConfigManager;
import org.plugin.skinstools.processor.SkinProcessor;


import java.util.logging.Logger;

/**
 * 玩家事件监听器
 * Player Event Listener
 * 
 * 监听玩家加入和退出事件，自动处理皮肤应用
 * Listens to player join and quit events, automatically handles skin application
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class PlayerListener implements Listener {
    
    private final SkinsTools plugin;
    private final ConfigManager configManager;
    private final SkinProcessor skinProcessor;
    private final Logger logger;
    
    public PlayerListener(@NotNull SkinsTools plugin,
                         @NotNull ConfigManager configManager,
                         @NotNull SkinProcessor skinProcessor) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.skinProcessor = skinProcessor;
        this.logger = plugin.getPluginLogger();
    }
    
    /**
     * 玩家加入事件处理
     * Player join event handler
     * 
     * @param event 玩家加入事件 Player join event
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerJoin(@NotNull PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        if (!configManager.isAutoApply()) {
            if (configManager.isDebugEnabled()) {
                logger.info("自动应用皮肤已禁用，跳过: " + player.getName());
                logger.info("Auto apply skin disabled, skipping: " + player.getName());
            }
            return;
        }
        
        if (configManager.isDebugEnabled()) {
            logger.info("玩家加入，准备处理皮肤: " + player.getName());
            logger.info("Player joined, preparing to process skin: " + player.getName());
        }
        
        // 根据配置的延迟时间处理皮肤
        int joinDelay = configManager.getJoinDelay();
        
        if (joinDelay > 0) {
            // 延迟处理
            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                processSkinForPlayer(player);
            }, joinDelay);
        } else {
            // 立即处理
            processSkinForPlayer(player);
        }
    }
    
    /**
     * 玩家退出事件处理
     * Player quit event handler
     * 
     * @param event 玩家退出事件 Player quit event
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(@NotNull PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        if (configManager.isDebugEnabled()) {
            logger.info("玩家退出: " + player.getName());
            logger.info("Player quit: " + player.getName());
        }
        
        // 这里可以添加一些清理逻辑，比如清理临时数据等
        // 但通常不需要清理缓存，因为缓存是持久的
    }
    
    /**
     * 为玩家处理皮肤
     * Process skin for player
     * 
     * @param player 玩家 Player
     */
    private void processSkinForPlayer(@NotNull Player player) {
        if (!player.isOnline()) {
            if (configManager.isDebugEnabled()) {
                logger.info("玩家已离线，跳过皮肤处理: " + player.getName());
                logger.info("Player offline, skipping skin processing: " + player.getName());
            }
            return;
        }
        
        String updateMode = configManager.getUpdateMode();
        
        switch (updateMode.toUpperCase()) {
            case "IMMEDIATE":
                processImmediately(player);
                break;
            case "DELAYED":
                processDelayed(player);
                break;
            case "NEXT_JOIN":
                processNextJoin(player);
                break;
            default:
                logger.warning("未知的更新模式: " + updateMode + "，使用默认模式 DELAYED");
                logger.warning("Unknown update mode: " + updateMode + ", using default DELAYED");
                processDelayed(player);
                break;
        }
    }
    
    /**
     * 立即处理皮肤
     * Process skin immediately
     * 
     * @param player 玩家 Player
     */
    private void processImmediately(@NotNull Player player) {
        if (configManager.isDebugEnabled()) {
            logger.info("立即处理皮肤: " + player.getName());
            logger.info("Processing skin immediately: " + player.getName());
        }
        
        // 在主线程中处理
        Bukkit.getScheduler().runTask(plugin, () -> {
            if (player.isOnline()) {
                handleSkinProcessing(player);
            }
        });
    }
    
    /**
     * 延迟处理皮肤
     * Process skin with delay
     * 
     * @param player 玩家 Player
     */
    private void processDelayed(@NotNull Player player) {
        if (configManager.isDebugEnabled()) {
            logger.info("延迟处理皮肤: " + player.getName());
            logger.info("Processing skin with delay: " + player.getName());
        }
        
        // 延迟1秒处理，给玩家足够的时间完全加载
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            if (player.isOnline()) {
                handleSkinProcessing(player);
            }
        }, 20L); // 20 ticks = 1 second
    }
    
    /**
     * 下次加入时处理皮肤
     * Process skin on next join
     * 
     * @param player 玩家 Player
     */
    private void processNextJoin(@NotNull Player player) {
        if (configManager.isDebugEnabled()) {
            logger.info("标记为下次加入时处理皮肤: " + player.getName());
            logger.info("Marked for next join skin processing: " + player.getName());
        }
        
        // 这种模式下，我们只在玩家没有有效皮肤时才处理
        if (!skinProcessor.hasValidSkin(player)) {
            handleSkinProcessing(player);
        }
    }
    
    /**
     * 处理皮肤应用
     * Handle skin processing
     * 
     * @param player 玩家 Player
     */
    private void handleSkinProcessing(@NotNull Player player) {
        if (configManager.isAsyncEnabled()) {
            // 异步处理
            skinProcessor.processPlayerSkinAsync(player)
                .thenAccept(success -> {
                    if (success) {
                        if (configManager.isDebugEnabled()) {
                            logger.info("皮肤处理成功: " + player.getName());
                            logger.info("Skin processing successful: " + player.getName());
                        }
                        
                        // 发送成功消息给玩家
                        sendMessageToPlayer(player, "skin-updated");
                    } else {
                        if (configManager.isDebugEnabled()) {
                            logger.info("皮肤处理失败: " + player.getName());
                            logger.info("Skin processing failed: " + player.getName());
                        }
                        
                        // 发送失败消息给玩家
                        sendMessageToPlayer(player, "skin-not-found");
                    }
                })
                .exceptionally(throwable -> {
                    logger.warning("皮肤处理异常: " + player.getName() + " - " + throwable.getMessage());
                    logger.warning("Skin processing exception: " + player.getName() + " - " + throwable.getMessage());
                    
                    // 发送错误消息给玩家
                    sendErrorMessageToPlayer(player, throwable.getMessage());
                    return null;
                });
        } else {
            // 同步处理
            try {
                boolean success = skinProcessor.processPlayerSkin(player);
                
                if (success) {
                    if (configManager.isDebugEnabled()) {
                        logger.info("皮肤处理成功: " + player.getName());
                        logger.info("Skin processing successful: " + player.getName());
                    }
                    
                    sendMessageToPlayer(player, "skin-updated");
                } else {
                    if (configManager.isDebugEnabled()) {
                        logger.info("皮肤处理失败: " + player.getName());
                        logger.info("Skin processing failed: " + player.getName());
                    }
                    
                    sendMessageToPlayer(player, "skin-not-found");
                }
                
            } catch (Exception e) {
                logger.warning("皮肤处理异常: " + player.getName() + " - " + e.getMessage());
                logger.warning("Skin processing exception: " + player.getName() + " - " + e.getMessage());
                
                sendErrorMessageToPlayer(player, e.getMessage());
            }
        }
    }
    
    /**
     * 发送消息给玩家
     * Send message to player
     * 
     * @param player 玩家 Player
     * @param messageKey 消息键 Message key
     */
    private void sendMessageToPlayer(@NotNull Player player, @NotNull String messageKey) {
        if (!player.isOnline()) {
            return;
        }
        
        try {
            String prefix = configManager.getPrefix();
            String message = configManager.getMessage(messageKey);
            String fullMessage = prefix + message;
            
            // 在主线程中发送消息
            Bukkit.getScheduler().runTask(plugin, () -> {
                if (player.isOnline()) {
                    player.sendMessage(fullMessage.replace("&", "§"));
                }
            });
            
        } catch (Exception e) {
            logger.warning("发送消息失败: " + e.getMessage());
            logger.warning("Failed to send message: " + e.getMessage());
        }
    }
    
    /**
     * 发送错误消息给玩家
     * Send error message to player
     * 
     * @param player 玩家 Player
     * @param error 错误信息 Error message
     */
    private void sendErrorMessageToPlayer(@NotNull Player player, @NotNull String error) {
        if (!player.isOnline()) {
            return;
        }
        
        try {
            String prefix = configManager.getPrefix();
            String message = configManager.getMessage("error-occurred", "&c发生错误: {error}");
            String fullMessage = prefix + message.replace("{error}", error);
            
            // 在主线程中发送消息
            Bukkit.getScheduler().runTask(plugin, () -> {
                if (player.isOnline()) {
                    player.sendMessage(fullMessage.replace("&", "§"));
                }
            });
            
        } catch (Exception e) {
            logger.warning("发送错误消息失败: " + e.getMessage());
            logger.warning("Failed to send error message: " + e.getMessage());
        }
    }
}
