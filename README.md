# SkinsTools

一个用于本地皮肤缓存和管理的Minecraft Paper插件，支持与Blessing Skin Server集成。
 **注意**：本插件由 AI 开发。

A Minecraft Paper plugin for local skin caching and management with Blessing Skin Server integration.
**Note**: This plugin is developed by AI.

## 功能特性 Features

### 中文
- 🎨 **自动皮肤应用** - 玩家加入时自动获取并应用皮肤
- 💾 **多层缓存系统** - 内存缓存 + SQLite数据库缓存
- 🌐 **Blessing Skin Server集成** - 基于角色名从皮肤站获取皮肤数据
- ⚡ **异步处理** - 不阻塞主线程的异步皮肤处理
- 🔧 **灵活配置** - 丰富的配置选项和自定义设置
- 📊 **缓存管理** - 自动清理过期缓存，支持手动管理
- 🎯 **命令支持** - 完整的命令系统和权限管理
- 🔄 **实时更新** - 支持实时刷新和设置皮肤
- 👤 **角色名匹配** - 使用玩家名作为Blessing Skin Server的角色名

### English
- 🎨 **Automatic Skin Application** - Automatically fetch and apply skins when players join
- 💾 **Multi-tier Caching System** - Memory cache + SQLite database cache
- 🌐 **Blessing Skin Server Integration** - Character name-based skin data fetching from skin servers
- ⚡ **Async Processing** - Non-blocking async skin processing
- 🔧 **Flexible Configuration** - Rich configuration options and custom settings
- 📊 **Cache Management** - Automatic expired cache cleanup with manual management support
- 🎯 **Command Support** - Complete command system and permission management
- 🔄 **Real-time Updates** - Support for real-time skin refresh and setting
- 👤 **Character Name Matching** - Uses player name as character name for Blessing Skin Server

## 重要说明 Important Notes

### 角色名匹配机制 Character Name Matching

本插件基于 **角色名** 而非 UUID 来获取皮肤，这是因为 Blessing Skin Server 使用角色名作为皮肤标识符。

This plugin fetches skins based on **character names** rather than UUIDs, as Blessing Skin Server uses character names as skin identifiers.

- **玩家名 = 角色名**: 插件将玩家的游戏内名称作为 Blessing Skin Server 上的角色名
- **Player Name = Character Name**: Plugin uses the player's in-game name as the character name on Blessing Skin Server
- **API端点 API Endpoint**: `/api/profiles/minecraft/{characterName}`

### 工作流程 Workflow

1. 玩家加入服务器 Player joins server
2. 插件使用玩家名作为角色名查询皮肤站 Plugin queries skin server using player name as character name
3. 获取皮肤数据并缓存 Fetch skin data and cache it
4. 应用皮肤到玩家 Apply skin to player

## 系统要求 Requirements

- **Minecraft版本 Minecraft Version**: 1.20.1+
- **服务端 Server**: Paper/Purpur/Folia
- **Java版本 Java Version**: 17+
- **Blessing Skin Server**: 任何兼容版本 Any compatible version

## 安装 Installation

1. 下载最新版本的SkinsTools插件
   Download the latest version of SkinsTools plugin

2. 将插件文件放入服务器的 `plugins` 文件夹
   Place the plugin file in your server's `plugins` folder

3. 重启服务器或使用 `/reload` 命令
   Restart the server or use `/reload` command

4. 编辑 `plugins/SkinsTools/config.yml` 配置文件
   Edit the `plugins/SkinsTools/config.yml` configuration file

5. 设置你的Blessing Skin Server URL
   Set your Blessing Skin Server URL

6. 重载配置: `/skinstools reload`
   Reload configuration: `/skinstools reload`

## 配置 Configuration

### 基本配置 Basic Configuration

```yaml
# Blessing Skin Server配置
blessing-skin:
  url: "https://your-skin-server.com"  # 你的皮肤站URL
  timeout: 5000                        # API超时时间(毫秒)
  retry-attempts: 3                    # 重试次数

# 缓存配置
cache:
  enabled: true                        # 启用缓存
  memory-size: 1000                    # 内存缓存大小
  expiration-hours: 24                 # 缓存过期时间(小时)

# 皮肤处理配置
skin-processing:
  auto-apply: true                     # 自动应用皮肤
  join-delay: 20                       # 加入延迟(tick)
  update-mode: "DELAYED"               # 更新模式
```

### 更新模式 Update Modes

- `IMMEDIATE` - 立即更新 Immediate update
- `DELAYED` - 延迟更新 Delayed update (推荐 Recommended)
- `NEXT_JOIN` - 下次加入时更新 Update on next join

## 命令 Commands

### 管理命令 Admin Commands

```
/skinstools reload              # 重载配置
/skinstools cache clear         # 清理所有缓存
/skinstools cache stats         # 查看缓存统计
/skinstools cache clean         # 清理过期缓存
/skinstools refresh [player]    # 刷新皮肤
/skinstools info               # 查看插件信息
/skinstools help               # 显示帮助
```

### 用户命令 User Commands

```
/skin                          # 刷新自己的皮肤
/skin <皮肤名称>                # 设置自己的皮肤
/skin <玩家> <皮肤名称>          # 设置其他玩家的皮肤
```

## 权限 Permissions

```yaml
skinstools.admin              # 管理员权限 (默认: op)
skinstools.use               # 使用皮肤命令 (默认: true)
skinstools.refresh           # 刷新自己的皮肤 (默认: true)
skinstools.refresh.others    # 刷新其他玩家的皮肤 (默认: op)
```

## API使用 API Usage

### 获取插件实例 Get Plugin Instance

```java
SkinsTools plugin = SkinsTools.getInstance();
SkinProcessor processor = plugin.getSkinProcessor();
SkinCache cache = plugin.getSkinCache();
```

### 处理玩家皮肤 Process Player Skin

```java
// 异步处理
processor.processPlayerSkinAsync(player).thenAccept(success -> {
    if (success) {
        player.sendMessage("皮肤已更新！");
    }
});

// 同步处理
boolean success = processor.processPlayerSkin(player);
```

### 缓存操作 Cache Operations

```java
// 获取皮肤数据
SkinData skinData = cache.getSkinData(playerUUID);

// 缓存皮肤数据
cache.cacheSkinData(skinData);

// 清理缓存
cache.clearAllCache();
```

## 故障排除 Troubleshooting

### 常见问题 Common Issues

1. **皮肤不显示 Skins not showing**
   - 检查Blessing Skin Server URL是否正确
   - 确认网络连接正常
   - 查看控制台错误日志

2. **缓存问题 Cache issues**
   - 使用 `/skinstools cache clear` 清理缓存
   - 检查数据库文件权限
   - 重启服务器

3. **性能问题 Performance issues**
   - 调整缓存大小设置
   - 启用异步处理
   - 增加API超时时间

### 调试模式 Debug Mode

在 `config.yml` 中启用调试模式：

```yaml
logging:
  debug: true
  log-requests: true
  log-cache: true
```

## 开发 Development

### 构建项目 Build Project

```bash
git clone https://github.com/NSrank/SkinsTools.git
cd SkinsTools
mvn clean package
```

### 依赖 Dependencies

- Paper API 1.20.1
- OkHttp 4.12.0
- Gson 2.10.1
- SQLite JDBC 3.44.1.0
- HikariCP 5.1.0
- Caffeine 3.1.8

## 开发信息

- **作者**: NSrank & Augment
- **版本**: 1.0.0
- **开源协议**: MIT License
- **GitHub**: https://github.com/NSrank/SkinsTools

## 许可证 License

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 贡献 Contributing

欢迎提交问题报告和功能请求！

Welcome to submit issue reports and feature requests!

## 支持 Support

如果你遇到问题或需要帮助，请：

If you encounter problems or need help, please:

1. 查看文档和常见问题 Check documentation and FAQ
2. 搜索现有的问题 Search existing issues
3. 创建新的问题报告 Create a new issue report

## 更新日志 Changelog

### v1.0.0
- 初始版本发布 Initial release
- 基本皮肤缓存和管理功能 Basic skin caching and management
- Blessing Skin Server集成 Blessing Skin Server integration
- 完整的命令系统 Complete command system
- 多层缓存支持 Multi-tier cache support
