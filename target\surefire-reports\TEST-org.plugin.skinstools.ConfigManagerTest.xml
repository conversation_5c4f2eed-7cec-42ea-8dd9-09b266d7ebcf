<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="org.plugin.skinstools.ConfigManagerTest" time="1.821" tests="9" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-booter\3.2.5\surefire-booter-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-api\3.2.5\surefire-api-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-logger-api\3.2.5\surefire-logger-api-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-shared-utils\3.2.5\surefire-shared-utils-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-extensions-spi\3.2.5\surefire-extensions-spi-3.2.5.jar;D:\MinecraftPlugins\SkinsTools\target\test-classes;D:\MinecraftPlugins\SkinsTools\target\classes;C:\Users\<USER>\.m2\repository\io\papermc\paper\paper-api\1.20.1-R0.1-SNAPSHOT\paper-api-1.20.1-R0.1-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\net\md-5\bungeecord-chat\1.20-R0.1-deprecated+build.14\bungeecord-chat-1.20-R0.1-deprecated+build.14.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.0\snakeyaml-2.0.jar;C:\Users\<USER>\.m2\repository\org\joml\joml\1.10.5\joml-1.10.5.jar;C:\Users\<USER>\.m2\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;C:\Users\<USER>\.m2\repository\it\unimi\dsi\fastutil\8.5.6\fastutil-8.5.6.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.1\log4j-api-2.17.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.8.0-beta4\slf4j-api-1.8.0-beta4.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-resolver-provider\3.8.5\maven-resolver-provider-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\3.8.5\maven-model-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model-builder\3.8.5\maven-model-builder-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.26\plexus-interpolation-1.26.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\3.8.5\maven-artifact-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-builder-support\3.8.5\maven-builder-support-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\sisu\org.eclipse.sisu.inject\0.3.5\org.eclipse.sisu.inject-0.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\3.8.5\maven-repository-metadata-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-api\1.6.3\maven-resolver-api-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-spi\1.6.3\maven-resolver-spi-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-util\1.6.3\maven-resolver-util-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-impl\1.6.3\maven-resolver-impl-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.8.1\commons-lang3-3.8.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\3.3.0\plexus-utils-3.3.0.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-api\4.14.0\adventure-api-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-key\4.14.0\adventure-key-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-api\1.3.0\examination-api-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-string\1.3.0\examination-string-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-minimessage\4.14.0\adventure-text-minimessage-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson\4.14.0\adventure-text-serializer-gson-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-json\4.14.0\adventure-text-serializer-json-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-legacy\4.14.0\adventure-text-serializer-legacy-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-plain\4.14.0\adventure-text-serializer-plain-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-logger-slf4j\4.14.0\adventure-text-logger-slf4j-4.14.0.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.21.0\checker-qual-3.21.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.4\asm-9.4.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-commons\9.4\asm-commons-9.4.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-tree\9.4\asm-tree-9.4.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.10\kotlin-stdlib-common-1.9.10.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.8.21\kotlin-stdlib-jdk8-1.8.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.8.21\kotlin-stdlib-1.8.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.8.21\kotlin-stdlib-jdk7-1.8.21.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\org\xerial\sqlite-jdbc\3.44.1.0\sqlite-jdbc-3.44.1.0.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.1.0\HikariCP-5.1.0.jar;C:\Users\<USER>\.m2\repository\com\github\ben-manes\caffeine\caffeine\3.1.8\caffeine-3.1.8.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.21.1\error_prone_annotations-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\24.1.0\annotations-24.1.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.8.0\mockito-core-5.8.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.8.0\mockito-junit-jupiter-5.8.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-junit-platform\3.2.5\surefire-junit-platform-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\common-java5\3.2.5\common-java5-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-launcher\1.10.1\junit-platform-launcher-1.10.1.jar"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-17\bin"/>
    <property name="sun.java.command" value="org.apache.maven.surefire.booter.ForkedBooter C:\Users\<USER>\AppData\Local\Temp\surefire17719554572617396310 2025-08-24T08-02-19_463-jvmRun1 surefire-20250824080219751_1tmp surefire_0-20250824080219751_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="D:\MinecraftPlugins\SkinsTools\target\test-classes;D:\MinecraftPlugins\SkinsTools\target\classes;C:\Users\<USER>\.m2\repository\io\papermc\paper\paper-api\1.20.1-R0.1-SNAPSHOT\paper-api-1.20.1-R0.1-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\net\md-5\bungeecord-chat\1.20-R0.1-deprecated+build.14\bungeecord-chat-1.20-R0.1-deprecated+build.14.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.0\snakeyaml-2.0.jar;C:\Users\<USER>\.m2\repository\org\joml\joml\1.10.5\joml-1.10.5.jar;C:\Users\<USER>\.m2\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;C:\Users\<USER>\.m2\repository\it\unimi\dsi\fastutil\8.5.6\fastutil-8.5.6.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.1\log4j-api-2.17.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.8.0-beta4\slf4j-api-1.8.0-beta4.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-resolver-provider\3.8.5\maven-resolver-provider-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\3.8.5\maven-model-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model-builder\3.8.5\maven-model-builder-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.26\plexus-interpolation-1.26.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\3.8.5\maven-artifact-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-builder-support\3.8.5\maven-builder-support-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\sisu\org.eclipse.sisu.inject\0.3.5\org.eclipse.sisu.inject-0.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\3.8.5\maven-repository-metadata-3.8.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-api\1.6.3\maven-resolver-api-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-spi\1.6.3\maven-resolver-spi-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-util\1.6.3\maven-resolver-util-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-impl\1.6.3\maven-resolver-impl-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.8.1\commons-lang3-3.8.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\3.3.0\plexus-utils-3.3.0.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-api\4.14.0\adventure-api-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-key\4.14.0\adventure-key-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-api\1.3.0\examination-api-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-string\1.3.0\examination-string-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-minimessage\4.14.0\adventure-text-minimessage-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson\4.14.0\adventure-text-serializer-gson-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-json\4.14.0\adventure-text-serializer-json-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-legacy\4.14.0\adventure-text-serializer-legacy-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-plain\4.14.0\adventure-text-serializer-plain-4.14.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-logger-slf4j\4.14.0\adventure-text-logger-slf4j-4.14.0.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.21.0\checker-qual-3.21.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.4\asm-9.4.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-commons\9.4\asm-commons-9.4.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-tree\9.4\asm-tree-9.4.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.10\kotlin-stdlib-common-1.9.10.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.8.21\kotlin-stdlib-jdk8-1.8.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.8.21\kotlin-stdlib-1.8.21.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.8.21\kotlin-stdlib-jdk7-1.8.21.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\org\xerial\sqlite-jdbc\3.44.1.0\sqlite-jdbc-3.44.1.0.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.1.0\HikariCP-5.1.0.jar;C:\Users\<USER>\.m2\repository\com\github\ben-manes\caffeine\caffeine\3.1.8\caffeine-3.1.8.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.21.1\error_prone_annotations-2.21.1.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\24.1.0\annotations-24.1.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.8.0\mockito-core-5.8.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.8.0\mockito-junit-jupiter-5.8.0.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-17"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\MinecraftPlugins\SkinsTools"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.7+8-LTS-224"/>
    <property name="user.name" value="user"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.7"/>
    <property name="user.dir" value="D:\MinecraftPlugins\SkinsTools"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\apache-maven-3.9.9\bin;;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\Program Files\IntelliJ IDEA Community Edition 2023.3.1\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.7+8-LTS-224"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testBlessingSkinConfiguration" classname="org.plugin.skinstools.ConfigManagerTest" time="1.669"/>
  <testcase name="testCacheConfiguration" classname="org.plugin.skinstools.ConfigManagerTest" time="0.021"/>
  <testcase name="testDefaultValues" classname="org.plugin.skinstools.ConfigManagerTest" time="0.012"/>
  <testcase name="testLoggingConfiguration" classname="org.plugin.skinstools.ConfigManagerTest" time="0.013"/>
  <testcase name="testSkinProcessingConfiguration" classname="org.plugin.skinstools.ConfigManagerTest" time="0.01"/>
  <testcase name="testCompatibilityConfiguration" classname="org.plugin.skinstools.ConfigManagerTest" time="0.008"/>
  <testcase name="testCacheExpirationValidation" classname="org.plugin.skinstools.ConfigManagerTest" time="0.01"/>
  <testcase name="testPerformanceConfiguration" classname="org.plugin.skinstools.ConfigManagerTest" time="0.009"/>
  <testcase name="testMessagesConfiguration" classname="org.plugin.skinstools.ConfigManagerTest" time="0.008"/>
</testsuite>