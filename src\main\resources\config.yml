# SkinsTools 配置文件
# SkinsTools Configuration File

# Blessing Skin Server 配置
# Blessing Skin Server Configuration
blessing-skin:
  # 皮肤站服务器URL (不要以/结尾)
  # Skin server URL (do not end with /)
  url: "https://skin.example.com"
  
  # API超时时间 (毫秒)
  # API timeout (milliseconds)
  timeout: 5000
  
  # 重试次数
  # Retry attempts
  retry-attempts: 3
  
  # 用户代理
  # User agent
  user-agent: "SkinsTools/1.0.0"

# 缓存配置
# Cache Configuration
cache:
  # 启用本地缓存
  # Enable local cache
  enabled: true
  
  # 内存缓存大小 (最大条目数)
  # Memory cache size (max entries)
  memory-size: 1000
  
  # 缓存过期时间 (小时)
  # Cache expiration time (hours)
  expiration-hours: 24
  
  # 数据库缓存
  # Database cache
  database:
    # 启用数据库缓存
    # Enable database cache
    enabled: true
    
    # 数据库文件路径 (相对于插件数据文件夹)
    # Database file path (relative to plugin data folder)
    file: "skins.db"
    
    # 连接池配置
    # Connection pool configuration
    pool:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000

# 皮肤处理配置
# Skin Processing Configuration
skin-processing:
  # 自动应用皮肤
  # Auto apply skins
  auto-apply: true
  
  # 玩家加入延迟 (tick, 20 tick = 1秒)
  # Player join delay (ticks, 20 ticks = 1 second)
  join-delay: 20
  
  # 默认皮肤 (当无法获取皮肤时使用)
  # Default skin (used when skin cannot be retrieved)
  default-skin: "Steve"
  
  # 皮肤更新模式
  # Skin update mode
  # IMMEDIATE - 立即更新
  # DELAYED - 延迟更新
  # NEXT_JOIN - 下次加入时更新
  update-mode: "DELAYED"

# 日志配置
# Logging Configuration
logging:
  # 启用调试模式
  # Enable debug mode
  debug: false
  
  # 记录API请求
  # Log API requests
  log-requests: false
  
  # 记录缓存操作
  # Log cache operations
  log-cache: false

# 性能配置
# Performance Configuration
performance:
  # 异步处理
  # Async processing
  async: true
  
  # 线程池大小
  # Thread pool size
  thread-pool-size: 4
  
  # 批量处理大小
  # Batch processing size
  batch-size: 10

# 兼容性配置
# Compatibility Configuration
compatibility:
  # 与其他皮肤插件的兼容性
  # Compatibility with other skin plugins
  check-other-plugins: true
  
  # 支持的皮肤格式
  # Supported skin formats
  supported-formats:
    - "png"
    - "jpg"
    - "jpeg"

# 消息配置
# Messages Configuration
messages:
  prefix: "&6[SkinsTools] &r"
  skin-updated: "&a皮肤已更新！"
  skin-not-found: "&c未找到皮肤！"
  cache-cleared: "&a缓存已清理！"
  config-reloaded: "&a配置已重载！"
  no-permission: "&c你没有权限执行此命令！"
  player-not-found: "&c玩家未找到！"
  processing: "&e正在处理皮肤..."
  error-occurred: "&c发生错误: {error}"
