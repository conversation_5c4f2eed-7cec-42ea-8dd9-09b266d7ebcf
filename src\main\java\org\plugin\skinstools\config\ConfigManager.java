package org.plugin.skinstools.config;

import org.bukkit.configuration.file.FileConfiguration;
import org.jetbrains.annotations.NotNull;
import org.plugin.skinstools.SkinsTools;

import java.util.List;
import java.util.logging.Logger;

/**
 * 配置管理器
 * Configuration Manager
 * 
 * 负责管理插件的所有配置项，提供类型安全的配置访问方法
 * Manages all plugin configuration items and provides type-safe configuration access methods
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ConfigManager {
    
    private final SkinsTools plugin;
    private final Logger logger;
    private FileConfiguration config;
    
    public ConfigManager(@NotNull SkinsTools plugin) {
        this.plugin = plugin;
        this.logger = plugin.getPluginLogger();
    }
    
    /**
     * 加载配置
     * Load configuration
     */
    public void loadConfig() {
        plugin.saveDefaultConfig();
        plugin.reloadConfig();
        config = plugin.getConfig();
        
        logger.info("配置文件已加载");
        logger.info("Configuration loaded");
        
        // 验证配置
        validateConfig();
    }
    
    /**
     * 重载配置
     * Reload configuration
     */
    public void reloadConfig() {
        plugin.reloadConfig();
        config = plugin.getConfig();
        
        logger.info("配置文件已重载");
        logger.info("Configuration reloaded");
        
        validateConfig();
    }
    
    /**
     * 验证配置
     * Validate configuration
     */
    private void validateConfig() {
        String url = getBlessingSkinUrl();
        if (url == null || url.trim().isEmpty() || url.equals("https://skin.example.com")) {
            logger.warning("警告: Blessing Skin Server URL 未正确配置！");
            logger.warning("Warning: Blessing Skin Server URL is not properly configured!");
        }
        
        if (getCacheExpirationHours() <= 0) {
            logger.warning("警告: 缓存过期时间配置无效，使用默认值24小时");
            logger.warning("Warning: Invalid cache expiration time, using default 24 hours");
        }
    }
    
    // Blessing Skin Server 配置
    // Blessing Skin Server Configuration
    
    @NotNull
    public String getBlessingSkinUrl() {
        return config.getString("blessing-skin.url", "https://skin.example.com");
    }
    
    public int getApiTimeout() {
        return config.getInt("blessing-skin.timeout", 5000);
    }
    
    public int getRetryAttempts() {
        return config.getInt("blessing-skin.retry-attempts", 3);
    }
    
    @NotNull
    public String getUserAgent() {
        return config.getString("blessing-skin.user-agent", "SkinsTools/1.0.0");
    }
    
    // 缓存配置
    // Cache Configuration
    
    public boolean isCacheEnabled() {
        return config.getBoolean("cache.enabled", true);
    }
    
    public int getMemoryCacheSize() {
        return config.getInt("cache.memory-size", 1000);
    }
    
    public int getCacheExpirationHours() {
        return Math.max(1, config.getInt("cache.expiration-hours", 24));
    }
    
    public boolean isDatabaseCacheEnabled() {
        return config.getBoolean("cache.database.enabled", true);
    }
    
    @NotNull
    public String getDatabaseFile() {
        return config.getString("cache.database.file", "skins.db");
    }
    
    public int getMaximumPoolSize() {
        return config.getInt("cache.database.pool.maximum-pool-size", 10);
    }
    
    public int getMinimumIdle() {
        return config.getInt("cache.database.pool.minimum-idle", 2);
    }
    
    public long getConnectionTimeout() {
        return config.getLong("cache.database.pool.connection-timeout", 30000);
    }
    
    // 皮肤处理配置
    // Skin Processing Configuration
    
    public boolean isAutoApply() {
        return config.getBoolean("skin-processing.auto-apply", true);
    }
    
    public int getJoinDelay() {
        return config.getInt("skin-processing.join-delay", 20);
    }
    
    @NotNull
    public String getDefaultSkin() {
        return config.getString("skin-processing.default-skin", "Steve");
    }
    
    @NotNull
    public String getUpdateMode() {
        return config.getString("skin-processing.update-mode", "DELAYED");
    }
    
    // 日志配置
    // Logging Configuration
    
    public boolean isDebugEnabled() {
        return config.getBoolean("logging.debug", false);
    }
    
    public boolean isLogRequestsEnabled() {
        return config.getBoolean("logging.log-requests", false);
    }
    
    public boolean isLogCacheEnabled() {
        return config.getBoolean("logging.log-cache", false);
    }
    
    // 性能配置
    // Performance Configuration
    
    public boolean isAsyncEnabled() {
        return config.getBoolean("performance.async", true);
    }
    
    public int getThreadPoolSize() {
        return config.getInt("performance.thread-pool-size", 4);
    }
    
    public int getBatchSize() {
        return config.getInt("performance.batch-size", 10);
    }
    
    // 兼容性配置
    // Compatibility Configuration
    
    public boolean isCheckOtherPlugins() {
        return config.getBoolean("compatibility.check-other-plugins", true);
    }
    
    @NotNull
    public List<String> getSupportedFormats() {
        return config.getStringList("compatibility.supported-formats");
    }
    
    // 消息配置
    // Messages Configuration
    
    @NotNull
    public String getPrefix() {
        return config.getString("messages.prefix", "&6[SkinsTools] &r");
    }
    
    @NotNull
    public String getMessage(@NotNull String key) {
        return config.getString("messages." + key, "&c消息未找到: " + key);
    }
    
    @NotNull
    public String getMessage(@NotNull String key, @NotNull String defaultValue) {
        return config.getString("messages." + key, defaultValue);
    }
}
