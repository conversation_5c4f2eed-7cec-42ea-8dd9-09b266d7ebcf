# Blessing Skin Server 集成说明

## 重要更新 Important Update

根据用户反馈，我们已经修正了对 Blessing Skin Server API 的理解和实现。Blessing Skin Server 确实是基于"角色名"而不是 UUID 来管理皮肤的。

Based on user feedback, we have corrected our understanding and implementation of the Blessing Skin Server API. Blessing Skin Server is indeed character name-based rather than UUID-based for skin management.

## 修改内容 Changes Made

### 1. API 客户端修正 API Client Corrections

**之前 Before:**
- 使用 UUID 作为主要标识符
- API 端点: `/api/profiles/minecraft/{uuid}`

**现在 Now:**
- 使用角色名作为主要标识符
- API 端点: `/api/profiles/minecraft/{characterName}`

### 2. 核心方法更新 Core Method Updates

```java
// 新增方法 New Methods
public CompletableFuture<SkinData> getSkinByCharacterName(String characterName)
public SkinData getSkinByCharacterNameSync(String characterName)

// 修改方法 Modified Methods  
public CompletableFuture<SkinData> getSkinByUsername(String username)
// 现在直接调用 getSkinByCharacterName，因为对于 Blessing Skin Server，用户名就是角色名
// Now directly calls getSkinByCharacterName, as username is character name for Blessing Skin Server
```

### 3. 缓存策略调整 Cache Strategy Adjustments

**缓存键 Cache Keys:**
- 主要基于角色名: `"name:" + characterName.toLowerCase()`
- 辅助基于生成的UUID: `"uuid:" + generatedUUID.toString()`

**新增缓存方法 New Cache Methods:**
```java
public void removeCacheByUsername(String username)
public CompletableFuture<Void> removeCacheByUsernameAsync(String username)
```

### 4. 皮肤处理流程 Skin Processing Workflow

**新的工作流程 New Workflow:**

1. **玩家加入 Player Joins**
   ```java
   String playerName = player.getName(); // 获取玩家名
   ```

2. **检查缓存 Check Cache**
   ```java
   SkinData cachedSkin = skinCache.getSkinDataByUsername(playerName);
   ```

3. **获取皮肤 Fetch Skin**
   ```java
   SkinData skinData = skinServerClient.getSkinByCharacterNameSync(playerName);
   ```

4. **应用皮肤 Apply Skin**
   ```java
   applySkinToPlayer(player, skinData);
   ```

## API 端点说明 API Endpoint Details

### Blessing Skin Server API 格式

```
GET /api/profiles/minecraft/{characterName}
```

**响应格式 Response Format:**
```json
{
  "id": "generated-uuid",
  "name": "character-name",
  "properties": [
    {
      "name": "textures",
      "value": "base64-encoded-texture-data",
      "signature": "optional-signature"
    }
  ]
}
```

### UUID 生成策略 UUID Generation Strategy

由于 Blessing Skin Server 基于角色名，我们使用以下方法生成确定性 UUID：

Since Blessing Skin Server is character name-based, we generate deterministic UUIDs using:

```java
private UUID generateUUIDFromName(String characterName) {
    return UUID.nameUUIDFromBytes(("OfflinePlayer:" + characterName).getBytes());
}
```

这确保了：
- 相同角色名总是生成相同的 UUID
- 与 Minecraft 离线模式的 UUID 生成方式兼容
- 缓存系统可以正常工作

This ensures:
- Same character name always generates the same UUID
- Compatible with Minecraft offline mode UUID generation
- Cache system works properly

## 配置更新 Configuration Updates

### config.yml 更新

```yaml
blessing-skin:
  # 皮肤站服务器URL (不要以/结尾)
  # 注意：插件将使用玩家名作为角色名来获取皮肤
  # Skin server URL (do not end with /)
  # Note: Plugin will use player name as character name to fetch skins
  url: "https://your-skin-server.com"
```

## 使用说明 Usage Instructions

### 1. 服务器管理员 Server Administrators

确保你的 Blessing Skin Server 配置正确：

Ensure your Blessing Skin Server is configured correctly:

1. **角色名匹配 Character Name Matching**
   - 玩家的游戏内名称应该与皮肤站上的角色名一致
   - Player's in-game name should match the character name on the skin server

2. **API 可访问性 API Accessibility**
   - 确保 `/api/profiles/minecraft/` 端点可访问
   - Ensure `/api/profiles/minecraft/` endpoint is accessible

### 2. 玩家 Players

**设置皮肤 Setting Skins:**

1. 在 Blessing Skin Server 上创建角色，角色名与游戏内名称相同
2. 上传皮肤到该角色
3. 在游戏中使用 `/skin` 命令刷新皮肤

**Setting Skins:**

1. Create a character on Blessing Skin Server with the same name as your in-game name
2. Upload skin to that character
3. Use `/skin` command in-game to refresh skin

## 故障排除 Troubleshooting

### 常见问题 Common Issues

1. **皮肤不显示 Skin Not Showing**
   ```
   原因：角色名不匹配
   解决：确保游戏内名称与皮肤站角色名完全一致
   
   Cause: Character name mismatch
   Solution: Ensure in-game name exactly matches skin server character name
   ```

2. **API 错误 API Errors**
   ```
   检查：/api/profiles/minecraft/{playerName} 是否可访问
   Check: Whether /api/profiles/minecraft/{playerName} is accessible
   ```

3. **缓存问题 Cache Issues**
   ```
   使用命令：/skinstools cache clear
   Use command: /skinstools cache clear
   ```

### 调试模式 Debug Mode

启用调试模式查看详细日志：

Enable debug mode for detailed logs:

```yaml
logging:
  debug: true
  log-requests: true
  log-cache: true
```

## 兼容性说明 Compatibility Notes

### 向后兼容 Backward Compatibility

- 现有的 UUID 基础缓存将自动转换为角色名基础
- 不需要清理现有缓存数据
- 所有现有命令和配置保持不变

- Existing UUID-based cache will automatically convert to character name-based
- No need to clean existing cache data
- All existing commands and configurations remain unchanged

### 性能影响 Performance Impact

- 角色名基础的缓存实际上更高效
- 减少了 UUID 查找步骤
- 更直接的 API 调用

- Character name-based caching is actually more efficient
- Eliminates UUID lookup steps
- More direct API calls

## 总结 Summary

这次更新使 SkinsTools 插件与 Blessing Skin Server 的实际工作方式完全一致，提供了更准确、更高效的皮肤管理体验。

This update aligns SkinsTools plugin with the actual working mechanism of Blessing Skin Server, providing a more accurate and efficient skin management experience.

**关键改进 Key Improvements:**
- ✅ 正确的 API 集成 Correct API integration
- ✅ 更高效的缓存策略 More efficient caching strategy  
- ✅ 简化的工作流程 Simplified workflow
- ✅ 更好的错误处理 Better error handling
- ✅ 完整的向后兼容 Full backward compatibility
