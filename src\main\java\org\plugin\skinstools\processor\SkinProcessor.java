package org.plugin.skinstools.processor;

import com.destroystokyo.paper.profile.PlayerProfile;
import com.destroystokyo.paper.profile.ProfileProperty;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.plugin.skinstools.api.SkinServerClient;
import org.plugin.skinstools.cache.SkinCache;
import org.plugin.skinstools.config.ConfigManager;
import org.plugin.skinstools.model.SkinData;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * 皮肤处理器
 * Skin Processor
 * 
 * 负责处理皮肤的获取、应用和管理
 * Responsible for skin retrieval, application and management
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SkinProcessor {
    
    private final ConfigManager configManager;
    private final SkinServerClient skinServerClient;
    private final SkinCache skinCache;
    private final Logger logger;
    
    public SkinProcessor(@NotNull ConfigManager configManager,
                        @NotNull SkinServerClient skinServerClient,
                        @NotNull SkinCache skinCache,
                        @NotNull Logger logger) {
        this.configManager = configManager;
        this.skinServerClient = skinServerClient;
        this.skinCache = skinCache;
        this.logger = logger;
    }
    
    /**
     * 异步处理玩家皮肤
     * Asynchronously process player skin
     * 
     * @param player 玩家 Player
     * @return CompletableFuture
     */
    @NotNull
    public CompletableFuture<Boolean> processPlayerSkinAsync(@NotNull Player player) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return processPlayerSkin(player);
            } catch (Exception e) {
                logger.warning("处理玩家皮肤失败: " + player.getName() + " - " + e.getMessage());
                logger.warning("Failed to process player skin: " + player.getName() + " - " + e.getMessage());
                return false;
            }
        });
    }
    
    /**
     * 处理玩家皮肤
     * Process player skin
     * 
     * @param player 玩家 Player
     * @return 是否成功 Whether successful
     */
    public boolean processPlayerSkin(@NotNull Player player) {
        UUID playerId = player.getUniqueId();
        String playerName = player.getName();
        
        if (configManager.isDebugEnabled()) {
            logger.info("开始处理玩家皮肤: " + playerName + " (" + playerId + ")");
            logger.info("Starting to process player skin: " + playerName + " (" + playerId + ")");
        }
        
        // 首先检查缓存
        SkinData cachedSkin = skinCache.getSkinData(playerId);
        if (cachedSkin != null) {
            if (configManager.isDebugEnabled()) {
                logger.info("使用缓存的皮肤: " + playerName);
                logger.info("Using cached skin: " + playerName);
            }
            return applySkinToPlayer(player, cachedSkin);
        }
        
        // 从皮肤服务器获取皮肤
        try {
            SkinData skinData = skinServerClient.getSkinByUUIDSync(playerId);
            if (skinData == null) {
                // 尝试使用用户名获取
                skinData = skinServerClient.getSkinByUsernameSync(playerName);
            }
            
            if (skinData != null) {
                // 缓存皮肤数据
                skinCache.cacheSkinData(skinData);
                
                // 应用皮肤
                return applySkinToPlayer(player, skinData);
            } else {
                if (configManager.isDebugEnabled()) {
                    logger.info("未找到皮肤数据: " + playerName);
                    logger.info("No skin data found: " + playerName);
                }
                return false;
            }
            
        } catch (Exception e) {
            logger.warning("获取皮肤数据失败: " + playerName + " - " + e.getMessage());
            logger.warning("Failed to get skin data: " + playerName + " - " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 异步刷新玩家皮肤
     * Asynchronously refresh player skin
     * 
     * @param player 玩家 Player
     * @return CompletableFuture
     */
    @NotNull
    public CompletableFuture<Boolean> refreshPlayerSkinAsync(@NotNull Player player) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return refreshPlayerSkin(player);
            } catch (Exception e) {
                logger.warning("刷新玩家皮肤失败: " + player.getName() + " - " + e.getMessage());
                logger.warning("Failed to refresh player skin: " + player.getName() + " - " + e.getMessage());
                return false;
            }
        });
    }
    
    /**
     * 刷新玩家皮肤
     * Refresh player skin
     * 
     * @param player 玩家 Player
     * @return 是否成功 Whether successful
     */
    public boolean refreshPlayerSkin(@NotNull Player player) {
        UUID playerId = player.getUniqueId();
        String playerName = player.getName();
        
        if (configManager.isDebugEnabled()) {
            logger.info("刷新玩家皮肤: " + playerName + " (" + playerId + ")");
            logger.info("Refreshing player skin: " + playerName + " (" + playerId + ")");
        }
        
        // 清除缓存
        skinCache.removeCache(playerId);
        
        // 重新处理皮肤
        return processPlayerSkin(player);
    }
    
    /**
     * 应用皮肤到玩家
     * Apply skin to player
     * 
     * @param player 玩家 Player
     * @param skinData 皮肤数据 Skin data
     * @return 是否成功 Whether successful
     */
    public boolean applySkinToPlayer(@NotNull Player player, @NotNull SkinData skinData) {
        try {
            PlayerProfile profile = player.getPlayerProfile();
            
            // 清除现有的皮肤属性
            profile.getProperties().removeIf(property -> "textures".equals(property.getName()));

            // 设置新的皮肤属性
            ProfileProperty textureProperty = new ProfileProperty(
                "textures",
                skinData.getTextureValue(),
                skinData.getTextureSignature()
            );

            profile.getProperties().add(textureProperty);
            
            // 应用到玩家
            player.setPlayerProfile(profile);
            
            // 更新玩家外观
            updatePlayerAppearance(player);
            
            if (configManager.isDebugEnabled()) {
                logger.info("皮肤已应用: " + player.getName());
                logger.info("Skin applied: " + player.getName());
            }
            
            return true;
            
        } catch (Exception e) {
            logger.warning("应用皮肤失败: " + player.getName() + " - " + e.getMessage());
            logger.warning("Failed to apply skin: " + player.getName() + " - " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 更新玩家外观
     * Update player appearance
     * 
     * @param player 玩家 Player
     */
    private void updatePlayerAppearance(@NotNull Player player) {
        // 在主线程中执行外观更新
        Bukkit.getScheduler().runTask(Bukkit.getPluginManager().getPlugin("SkinsTools"), () -> {
            try {
                // 隐藏玩家
                for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                    if (!onlinePlayer.equals(player)) {
                        onlinePlayer.hidePlayer(Bukkit.getPluginManager().getPlugin("SkinsTools"), player);
                    }
                }
                
                // 延迟显示玩家以确保皮肤更新
                Bukkit.getScheduler().runTaskLater(
                    Bukkit.getPluginManager().getPlugin("SkinsTools"), 
                    () -> {
                        for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                            if (!onlinePlayer.equals(player)) {
                                onlinePlayer.showPlayer(Bukkit.getPluginManager().getPlugin("SkinsTools"), player);
                            }
                        }
                        
                        if (configManager.isDebugEnabled()) {
                            logger.info("玩家外观已更新: " + player.getName());
                            logger.info("Player appearance updated: " + player.getName());
                        }
                    }, 
                    5L // 5 ticks delay
                );
                
            } catch (Exception e) {
                logger.warning("更新玩家外观失败: " + player.getName() + " - " + e.getMessage());
                logger.warning("Failed to update player appearance: " + player.getName() + " - " + e.getMessage());
            }
        });
    }
    
    /**
     * 异步根据皮肤名称设置玩家皮肤
     * Asynchronously set player skin by skin name
     * 
     * @param player 玩家 Player
     * @param skinName 皮肤名称 Skin name
     * @return CompletableFuture
     */
    @NotNull
    public CompletableFuture<Boolean> setSkinAsync(@NotNull Player player, @NotNull String skinName) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return setSkin(player, skinName);
            } catch (Exception e) {
                logger.warning("设置皮肤失败: " + player.getName() + " -> " + skinName + " - " + e.getMessage());
                logger.warning("Failed to set skin: " + player.getName() + " -> " + skinName + " - " + e.getMessage());
                return false;
            }
        });
    }
    
    /**
     * 根据皮肤名称设置玩家皮肤
     * Set player skin by skin name
     * 
     * @param player 玩家 Player
     * @param skinName 皮肤名称 Skin name
     * @return 是否成功 Whether successful
     */
    public boolean setSkin(@NotNull Player player, @NotNull String skinName) {
        if (configManager.isDebugEnabled()) {
            logger.info("设置玩家皮肤: " + player.getName() + " -> " + skinName);
            logger.info("Setting player skin: " + player.getName() + " -> " + skinName);
        }
        
        try {
            // 首先检查缓存
            SkinData cachedSkin = skinCache.getSkinDataByUsername(skinName);
            if (cachedSkin != null) {
                return applySkinToPlayer(player, cachedSkin);
            }
            
            // 从皮肤服务器获取皮肤
            SkinData skinData = skinServerClient.getSkinByUsernameSync(skinName);
            if (skinData != null) {
                // 缓存皮肤数据
                skinCache.cacheSkinData(skinData);
                
                // 应用皮肤
                return applySkinToPlayer(player, skinData);
            } else {
                if (configManager.isDebugEnabled()) {
                    logger.info("未找到皮肤: " + skinName);
                    logger.info("Skin not found: " + skinName);
                }
                return false;
            }
            
        } catch (Exception e) {
            logger.warning("设置皮肤失败: " + skinName + " - " + e.getMessage());
            logger.warning("Failed to set skin: " + skinName + " - " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查玩家是否有有效的皮肤
     * Check if player has valid skin
     * 
     * @param player 玩家 Player
     * @return 是否有有效皮肤 Whether has valid skin
     */
    public boolean hasValidSkin(@NotNull Player player) {
        try {
            PlayerProfile profile = player.getPlayerProfile();
            return profile.getProperties().stream()
                   .anyMatch(property -> "textures".equals(property.getName()));
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取玩家当前的皮肤数据
     * Get player's current skin data
     * 
     * @param player 玩家 Player
     * @return 皮肤数据 Skin data
     */
    @Nullable
    public SkinData getCurrentSkinData(@NotNull Player player) {
        try {
            PlayerProfile profile = player.getPlayerProfile();
            ProfileProperty textureProperty = profile.getProperties().stream()
                .filter(property -> "textures".equals(property.getName()))
                .findFirst()
                .orElse(null);

            if (textureProperty == null) {
                return null;
            }
            
            return new SkinData(
                player.getUniqueId(),
                player.getName(),
                textureProperty.getValue(),
                textureProperty.getSignature(),
                null, // 无法从属性中获取URL
                null, // 无法从属性中获取URL
                false // 无法从属性中获取模型信息
            );
            
        } catch (Exception e) {
            logger.warning("获取当前皮肤数据失败: " + player.getName() + " - " + e.getMessage());
            logger.warning("Failed to get current skin data: " + player.getName() + " - " + e.getMessage());
            return null;
        }
    }
}
