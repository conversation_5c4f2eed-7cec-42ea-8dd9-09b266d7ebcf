name: SkinsTools
version: '${project.version}'
main: org.plugin.skinstools.SkinsTools
api-version: '1.20'
load: STARTUP
author: NSrank & Augment
description: 'A Minecraft Paper plugin for local skin caching and management with Blessing Skin Server integration'
website: 'https://github.com/NSrank/SkinsTools'

commands:
  skinstools:
    description: 'Main SkinsTools command'
    aliases: [st, skins]
    usage: '/skinstools <reload|cache|refresh> [player]'
    permission: skinstools.admin
  skin:
    description: 'Set or refresh player skin'
    usage: '/skin [player] [skin_name]'
    permission: skinstools.use

permissions:
  skinstools.admin:
    description: 'Allows access to admin commands'
    default: op
  skinstools.use:
    description: 'Allows using skin commands'
    default: true
  skinstools.refresh:
    description: 'Allows refreshing own skin'
    default: true
  skinstools.refresh.others:
    description: 'Allows refreshing other players skins'
    default: op
