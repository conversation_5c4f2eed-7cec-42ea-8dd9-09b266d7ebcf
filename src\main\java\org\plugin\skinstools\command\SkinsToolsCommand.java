package org.plugin.skinstools.command;

import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.plugin.skinstools.SkinsTools;
import org.plugin.skinstools.cache.SkinCache;
import org.plugin.skinstools.config.ConfigManager;
import org.plugin.skinstools.processor.SkinProcessor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;

/**
 * SkinsTools主命令处理器
 * SkinsTools Main Command Handler
 * 
 * 处理插件的管理命令
 * Handles plugin management commands
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SkinsToolsCommand implements CommandExecutor, TabCompleter {
    
    private final SkinsTools plugin;
    private final ConfigManager configManager;
    private final SkinProcessor skinProcessor;
    private final SkinCache skinCache;
    private final Logger logger;
    
    public SkinsToolsCommand(@NotNull SkinsTools plugin,
                            @NotNull ConfigManager configManager,
                            @NotNull SkinProcessor skinProcessor,
                            @NotNull SkinCache skinCache) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.skinProcessor = skinProcessor;
        this.skinCache = skinCache;
        this.logger = plugin.getPluginLogger();
    }
    
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, 
                           @NotNull String label, @NotNull String[] args) {
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "reload":
                return handleReload(sender);
            case "cache":
                return handleCache(sender, args);
            case "refresh":
                return handleRefresh(sender, args);
            case "info":
                return handleInfo(sender);
            case "help":
                sendHelpMessage(sender);
                return true;
            default:
                sendMessage(sender, "&c未知命令: " + subCommand);
                sendMessage(sender, "&cUnknown command: " + subCommand);
                sendHelpMessage(sender);
                return true;
        }
    }
    
    /**
     * 处理重载命令
     * Handle reload command
     */
    private boolean handleReload(@NotNull CommandSender sender) {
        if (!sender.hasPermission("skinstools.admin")) {
            sendMessage(sender, configManager.getMessage("no-permission"));
            return true;
        }
        
        try {
            configManager.reloadConfig();
            sendMessage(sender, configManager.getMessage("config-reloaded"));
            
            logger.info("配置已被 " + sender.getName() + " 重载");
            logger.info("Configuration reloaded by " + sender.getName());
            
        } catch (Exception e) {
            sendMessage(sender, "&c重载配置失败: " + e.getMessage());
            sendMessage(sender, "&cFailed to reload config: " + e.getMessage());
            logger.warning("配置重载失败: " + e.getMessage());
        }
        
        return true;
    }
    
    /**
     * 处理缓存命令
     * Handle cache command
     */
    private boolean handleCache(@NotNull CommandSender sender, @NotNull String[] args) {
        if (!sender.hasPermission("skinstools.admin")) {
            sendMessage(sender, configManager.getMessage("no-permission"));
            return true;
        }
        
        if (args.length < 2) {
            sendMessage(sender, "&c用法: /skinstools cache <clear|stats|clean>");
            sendMessage(sender, "&cUsage: /skinstools cache <clear|stats|clean>");
            return true;
        }
        
        String cacheAction = args[1].toLowerCase();
        
        switch (cacheAction) {
            case "clear":
                return handleCacheClear(sender);
            case "stats":
                return handleCacheStats(sender);
            case "clean":
                return handleCacheClean(sender);
            default:
                sendMessage(sender, "&c未知缓存操作: " + cacheAction);
                sendMessage(sender, "&cUnknown cache action: " + cacheAction);
                return true;
        }
    }
    
    /**
     * 处理缓存清理命令
     * Handle cache clear command
     */
    private boolean handleCacheClear(@NotNull CommandSender sender) {
        sendMessage(sender, "&e正在清理缓存...");
        sendMessage(sender, "&eClearing cache...");
        
        skinCache.clearAllCacheAsync().thenRun(() -> {
            sendMessage(sender, configManager.getMessage("cache-cleared"));
            
            logger.info("缓存已被 " + sender.getName() + " 清理");
            logger.info("Cache cleared by " + sender.getName());
        });
        
        return true;
    }
    
    /**
     * 处理缓存统计命令
     * Handle cache stats command
     */
    private boolean handleCacheStats(@NotNull CommandSender sender) {
        SkinCache.CacheStats stats = skinCache.getCacheStats();
        
        sendMessage(sender, "&6=== 缓存统计 Cache Statistics ===");
        sendMessage(sender, "&e内存缓存 Memory Cache: &a" + stats.getMemorySize() + " &e条目 entries");
        sendMessage(sender, "&e数据库缓存 Database Cache: &a" + stats.getDatabaseSize() + " &e条目 entries");
        sendMessage(sender, "&e总计 Total: &a" + stats.getTotalSize() + " &e条目 entries");
        
        return true;
    }
    
    /**
     * 处理缓存清理命令
     * Handle cache clean command
     */
    private boolean handleCacheClean(@NotNull CommandSender sender) {
        sendMessage(sender, "&e正在清理过期缓存...");
        sendMessage(sender, "&eCleaning expired cache...");
        
        skinCache.cleanExpiredCacheAsync().thenAccept(cleaned -> {
            sendMessage(sender, "&a清理了 " + cleaned + " 条过期缓存");
            sendMessage(sender, "&aCleaned " + cleaned + " expired cache entries");
            
            logger.info("过期缓存已被 " + sender.getName() + " 清理，清理了 " + cleaned + " 条");
            logger.info("Expired cache cleaned by " + sender.getName() + ", cleaned " + cleaned + " entries");
        });
        
        return true;
    }
    
    /**
     * 处理刷新命令
     * Handle refresh command
     */
    private boolean handleRefresh(@NotNull CommandSender sender, @NotNull String[] args) {
        if (!sender.hasPermission("skinstools.refresh")) {
            sendMessage(sender, configManager.getMessage("no-permission"));
            return true;
        }
        
        Player targetPlayer;
        
        if (args.length >= 2) {
            // 刷新指定玩家
            if (!sender.hasPermission("skinstools.refresh.others")) {
                sendMessage(sender, configManager.getMessage("no-permission"));
                return true;
            }
            
            targetPlayer = Bukkit.getPlayer(args[1]);
            if (targetPlayer == null) {
                sendMessage(sender, configManager.getMessage("player-not-found"));
                return true;
            }
        } else {
            // 刷新自己
            if (!(sender instanceof Player)) {
                sendMessage(sender, "&c控制台必须指定玩家名称");
                sendMessage(sender, "&cConsole must specify player name");
                return true;
            }
            targetPlayer = (Player) sender;
        }
        
        sendMessage(sender, configManager.getMessage("processing"));
        
        skinProcessor.refreshPlayerSkinAsync(targetPlayer).thenAccept(success -> {
            if (success) {
                sendMessage(sender, "&a成功刷新 " + targetPlayer.getName() + " 的皮肤");
                sendMessage(sender, "&aSuccessfully refreshed " + targetPlayer.getName() + "'s skin");
                
                if (!sender.equals(targetPlayer)) {
                    sendMessage(targetPlayer, configManager.getMessage("skin-updated"));
                }
            } else {
                sendMessage(sender, "&c刷新 " + targetPlayer.getName() + " 的皮肤失败");
                sendMessage(sender, "&cFailed to refresh " + targetPlayer.getName() + "'s skin");
            }
        });
        
        return true;
    }
    
    /**
     * 处理信息命令
     * Handle info command
     */
    private boolean handleInfo(@NotNull CommandSender sender) {
        sendMessage(sender, "&6=== SkinsTools 信息 Information ===");
        sendMessage(sender, "&e版本 Version: &a" + plugin.getPluginMeta().getVersion());
        sendMessage(sender, "&e作者 Author: &a" + plugin.getPluginMeta().getAuthors().get(0));
        sendMessage(sender, "&e皮肤服务器 Skin Server: &a" + configManager.getBlessingSkinUrl());
        sendMessage(sender, "&e缓存状态 Cache Status: &a" + (configManager.isCacheEnabled() ? "启用 Enabled" : "禁用 Disabled"));
        sendMessage(sender, "&e自动应用 Auto Apply: &a" + (configManager.isAutoApply() ? "启用 Enabled" : "禁用 Disabled"));
        
        SkinCache.CacheStats stats = skinCache.getCacheStats();
        sendMessage(sender, "&e缓存条目 Cache Entries: &a" + stats.getTotalSize());
        
        return true;
    }
    
    /**
     * 发送帮助消息
     * Send help message
     */
    private void sendHelpMessage(@NotNull CommandSender sender) {
        sendMessage(sender, "&6=== SkinsTools 命令帮助 Command Help ===");
        sendMessage(sender, "&e/skinstools reload &7- &f重载配置 Reload configuration");
        sendMessage(sender, "&e/skinstools cache clear &7- &f清理所有缓存 Clear all cache");
        sendMessage(sender, "&e/skinstools cache stats &7- &f查看缓存统计 View cache statistics");
        sendMessage(sender, "&e/skinstools cache clean &7- &f清理过期缓存 Clean expired cache");
        sendMessage(sender, "&e/skinstools refresh [player] &7- &f刷新皮肤 Refresh skin");
        sendMessage(sender, "&e/skinstools info &7- &f查看插件信息 View plugin information");
        sendMessage(sender, "&e/skinstools help &7- &f显示此帮助 Show this help");
    }
    
    /**
     * 发送消息
     * Send message
     */
    private void sendMessage(@NotNull CommandSender sender, @NotNull String message) {
        String prefix = configManager.getPrefix();
        sender.sendMessage((prefix + message).replace("&", "§"));
    }
    
    @Override
    @Nullable
    public List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command,
                                    @NotNull String alias, @NotNull String[] args) {
        
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // 第一级子命令
            List<String> subCommands = Arrays.asList("reload", "cache", "refresh", "info", "help");
            for (String subCommand : subCommands) {
                if (subCommand.toLowerCase().startsWith(args[0].toLowerCase())) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2) {
            String subCommand = args[0].toLowerCase();
            
            if ("cache".equals(subCommand)) {
                // 缓存子命令
                List<String> cacheCommands = Arrays.asList("clear", "stats", "clean");
                for (String cacheCommand : cacheCommands) {
                    if (cacheCommand.toLowerCase().startsWith(args[1].toLowerCase())) {
                        completions.add(cacheCommand);
                    }
                }
            } else if ("refresh".equals(subCommand)) {
                // 玩家名称补全
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.getName().toLowerCase().startsWith(args[1].toLowerCase())) {
                        completions.add(player.getName());
                    }
                }
            }
        }
        
        return completions;
    }
}
