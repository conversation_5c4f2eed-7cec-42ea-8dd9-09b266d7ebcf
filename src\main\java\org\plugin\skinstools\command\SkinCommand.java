package org.plugin.skinstools.command;

import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.plugin.skinstools.SkinsTools;
import org.plugin.skinstools.config.ConfigManager;
import org.plugin.skinstools.processor.SkinProcessor;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * 皮肤命令处理器
 * Skin Command Handler
 * 
 * 处理皮肤设置和刷新命令
 * Handles skin setting and refresh commands
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SkinCommand implements CommandExecutor, TabCompleter {
    
    private final SkinsTools plugin;
    private final ConfigManager configManager;
    private final SkinProcessor skinProcessor;
    private final Logger logger;
    
    public SkinCommand(@NotNull SkinsTools plugin,
                      @NotNull ConfigManager configManager,
                      @NotNull SkinProcessor skinProcessor) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.skinProcessor = skinProcessor;
        this.logger = plugin.getPluginLogger();
    }
    
    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command,
                           @NotNull String label, @NotNull String[] args) {
        
        if (!sender.hasPermission("skinstools.use")) {
            sendMessage(sender, configManager.getMessage("no-permission"));
            return true;
        }
        
        if (args.length == 0) {
            // 刷新自己的皮肤
            if (!(sender instanceof Player)) {
                sendMessage(sender, "&c控制台必须指定玩家和皮肤名称");
                sendMessage(sender, "&cConsole must specify player and skin name");
                sendUsage(sender);
                return true;
            }
            
            return handleRefreshSkin((Player) sender);
        }
        
        if (args.length == 1) {
            if (sender instanceof Player) {
                // 设置自己的皮肤
                return handleSetSkin((Player) sender, args[0]);
            } else {
                sendMessage(sender, "&c控制台必须指定玩家名称");
                sendMessage(sender, "&cConsole must specify player name");
                sendUsage(sender);
                return true;
            }
        }
        
        if (args.length == 2) {
            // 设置指定玩家的皮肤
            if (!sender.hasPermission("skinstools.refresh.others")) {
                sendMessage(sender, configManager.getMessage("no-permission"));
                return true;
            }
            
            Player targetPlayer = Bukkit.getPlayer(args[0]);
            if (targetPlayer == null) {
                sendMessage(sender, configManager.getMessage("player-not-found"));
                return true;
            }
            
            return handleSetSkin(sender, targetPlayer, args[1]);
        }
        
        sendUsage(sender);
        return true;
    }
    
    /**
     * 处理刷新皮肤命令
     * Handle refresh skin command
     */
    private boolean handleRefreshSkin(@NotNull Player player) {
        if (!player.hasPermission("skinstools.refresh")) {
            sendMessage(player, configManager.getMessage("no-permission"));
            return true;
        }
        
        sendMessage(player, configManager.getMessage("processing"));
        
        skinProcessor.refreshPlayerSkinAsync(player).thenAccept(success -> {
            if (success) {
                sendMessage(player, configManager.getMessage("skin-updated"));
                
                if (configManager.isDebugEnabled()) {
                    logger.info("玩家 " + player.getName() + " 刷新了自己的皮肤");
                    logger.info("Player " + player.getName() + " refreshed their skin");
                }
            } else {
                sendMessage(player, configManager.getMessage("skin-not-found"));
            }
        }).exceptionally(throwable -> {
            sendMessage(player, "&c刷新皮肤时发生错误: " + throwable.getMessage());
            sendMessage(player, "&cError occurred while refreshing skin: " + throwable.getMessage());
            
            logger.warning("玩家 " + player.getName() + " 刷新皮肤时发生错误: " + throwable.getMessage());
            logger.warning("Error occurred while " + player.getName() + " refreshing skin: " + throwable.getMessage());
            
            return null;
        });
        
        return true;
    }
    
    /**
     * 处理设置皮肤命令（自己）
     * Handle set skin command (self)
     */
    private boolean handleSetSkin(@NotNull Player player, @NotNull String skinName) {
        if (!player.hasPermission("skinstools.use")) {
            sendMessage(player, configManager.getMessage("no-permission"));
            return true;
        }
        
        if (skinName.trim().isEmpty()) {
            sendMessage(player, "&c皮肤名称不能为空");
            sendMessage(player, "&cSkin name cannot be empty");
            return true;
        }
        
        sendMessage(player, configManager.getMessage("processing"));
        
        skinProcessor.setSkinAsync(player, skinName).thenAccept(success -> {
            if (success) {
                sendMessage(player, "&a成功设置皮肤为: " + skinName);
                sendMessage(player, "&aSuccessfully set skin to: " + skinName);
                
                if (configManager.isDebugEnabled()) {
                    logger.info("玩家 " + player.getName() + " 设置皮肤为: " + skinName);
                    logger.info("Player " + player.getName() + " set skin to: " + skinName);
                }
            } else {
                sendMessage(player, "&c未找到皮肤: " + skinName);
                sendMessage(player, "&cSkin not found: " + skinName);
            }
        }).exceptionally(throwable -> {
            sendMessage(player, "&c设置皮肤时发生错误: " + throwable.getMessage());
            sendMessage(player, "&cError occurred while setting skin: " + throwable.getMessage());
            
            logger.warning("玩家 " + player.getName() + " 设置皮肤 " + skinName + " 时发生错误: " + throwable.getMessage());
            logger.warning("Error occurred while " + player.getName() + " setting skin " + skinName + ": " + throwable.getMessage());
            
            return null;
        });
        
        return true;
    }
    
    /**
     * 处理设置皮肤命令（其他玩家）
     * Handle set skin command (other player)
     */
    private boolean handleSetSkin(@NotNull CommandSender sender, @NotNull Player targetPlayer, @NotNull String skinName) {
        if (skinName.trim().isEmpty()) {
            sendMessage(sender, "&c皮肤名称不能为空");
            sendMessage(sender, "&cSkin name cannot be empty");
            return true;
        }
        
        sendMessage(sender, configManager.getMessage("processing"));
        
        skinProcessor.setSkinAsync(targetPlayer, skinName).thenAccept(success -> {
            if (success) {
                sendMessage(sender, "&a成功为 " + targetPlayer.getName() + " 设置皮肤为: " + skinName);
                sendMessage(sender, "&aSuccessfully set " + targetPlayer.getName() + "'s skin to: " + skinName);
                
                sendMessage(targetPlayer, "&a你的皮肤已被设置为: " + skinName);
                sendMessage(targetPlayer, "&aYour skin has been set to: " + skinName);
                
                if (configManager.isDebugEnabled()) {
                    logger.info(sender.getName() + " 为玩家 " + targetPlayer.getName() + " 设置皮肤为: " + skinName);
                    logger.info(sender.getName() + " set " + targetPlayer.getName() + "'s skin to: " + skinName);
                }
            } else {
                sendMessage(sender, "&c未找到皮肤: " + skinName);
                sendMessage(sender, "&cSkin not found: " + skinName);
            }
        }).exceptionally(throwable -> {
            sendMessage(sender, "&c设置皮肤时发生错误: " + throwable.getMessage());
            sendMessage(sender, "&cError occurred while setting skin: " + throwable.getMessage());
            
            logger.warning(sender.getName() + " 为玩家 " + targetPlayer.getName() + " 设置皮肤 " + skinName + " 时发生错误: " + throwable.getMessage());
            logger.warning("Error occurred while " + sender.getName() + " setting " + targetPlayer.getName() + "'s skin " + skinName + ": " + throwable.getMessage());
            
            return null;
        });
        
        return true;
    }
    
    /**
     * 发送用法消息
     * Send usage message
     */
    private void sendUsage(@NotNull CommandSender sender) {
        sendMessage(sender, "&6=== 皮肤命令用法 Skin Command Usage ===");
        sendMessage(sender, "&e/skin &7- &f刷新自己的皮肤 Refresh your skin");
        sendMessage(sender, "&e/skin <皮肤名称> &7- &f设置自己的皮肤 Set your skin");
        sendMessage(sender, "&e/skin <玩家> <皮肤名称> &7- &f设置其他玩家的皮肤 Set other player's skin");
    }
    
    /**
     * 发送消息
     * Send message
     */
    private void sendMessage(@NotNull CommandSender sender, @NotNull String message) {
        String prefix = configManager.getPrefix();
        sender.sendMessage((prefix + message).replace("&", "§"));
    }
    
    @Override
    @Nullable
    public List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command,
                                    @NotNull String alias, @NotNull String[] args) {
        
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // 第一个参数可以是玩家名称或皮肤名称
            if (sender.hasPermission("skinstools.refresh.others")) {
                // 添加在线玩家名称
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.getName().toLowerCase().startsWith(args[0].toLowerCase())) {
                        completions.add(player.getName());
                    }
                }
            }
            
            // 这里可以添加常用皮肤名称的补全
            // 由于我们无法预知皮肤服务器上的所有皮肤名称，这里暂时不添加
            
        } else if (args.length == 2) {
            // 第二个参数是皮肤名称
            // 这里可以添加皮肤名称的补全逻辑
            // 由于我们无法预知皮肤服务器上的所有皮肤名称，这里暂时不添加
        }
        
        return completions;
    }
}
