package org.plugin.skinstools.database;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.plugin.skinstools.config.ConfigManager;
import org.plugin.skinstools.model.SkinData;

import java.io.File;
import java.sql.*;
import java.time.Instant;

import java.util.UUID;
import java.util.logging.Logger;

/**
 * 数据库管理器
 * Database Manager
 * 
 * 负责管理SQLite数据库连接和皮肤数据的持久化存储
 * Manages SQLite database connections and persistent storage of skin data
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class DatabaseManager {
    
    private final ConfigManager configManager;
    private final Logger logger;
    private final File dataFolder;
    private HikariDataSource dataSource;
    
    // SQL语句
    private static final String CREATE_TABLE_SQL = """
        CREATE TABLE IF NOT EXISTS skin_cache (
            player_id TEXT PRIMARY KEY,
            player_name TEXT NOT NULL,
            texture_value TEXT NOT NULL,
            texture_signature TEXT,
            skin_url TEXT,
            cape_url TEXT,
            is_slim INTEGER NOT NULL DEFAULT 0,
            created_at INTEGER NOT NULL,
            updated_at INTEGER NOT NULL
        )
        """;
    
    private static final String CREATE_INDEX_SQL = """
        CREATE INDEX IF NOT EXISTS idx_player_name ON skin_cache(player_name);
        CREATE INDEX IF NOT EXISTS idx_updated_at ON skin_cache(updated_at);
        """;
    
    private static final String INSERT_OR_REPLACE_SQL = """
        INSERT OR REPLACE INTO skin_cache 
        (player_id, player_name, texture_value, texture_signature, skin_url, cape_url, is_slim, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;
    
    private static final String SELECT_BY_UUID_SQL = """
        SELECT * FROM skin_cache WHERE player_id = ?
        """;
    
    private static final String SELECT_BY_NAME_SQL = """
        SELECT * FROM skin_cache WHERE player_name = ? ORDER BY updated_at DESC LIMIT 1
        """;
    
    private static final String DELETE_BY_UUID_SQL = """
        DELETE FROM skin_cache WHERE player_id = ?
        """;
    
    private static final String DELETE_EXPIRED_SQL = """
        DELETE FROM skin_cache WHERE updated_at < ?
        """;
    
    private static final String COUNT_ALL_SQL = """
        SELECT COUNT(*) FROM skin_cache
        """;
    
    public DatabaseManager(@NotNull ConfigManager configManager, @NotNull Logger logger, @NotNull File dataFolder) {
        this.configManager = configManager;
        this.logger = logger;
        this.dataFolder = dataFolder;
    }
    
    /**
     * 初始化数据库
     * Initialize database
     */
    public void initialize() {
        if (!configManager.isDatabaseCacheEnabled()) {
            logger.info("数据库缓存已禁用");
            logger.info("Database cache is disabled");
            return;
        }
        
        try {
            setupDataSource();
            createTables();
            logger.info("数据库初始化完成");
            logger.info("Database initialized successfully");
        } catch (Exception e) {
            logger.severe("数据库初始化失败: " + e.getMessage());
            logger.severe("Failed to initialize database: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 设置数据源
     * Setup data source
     */
    private void setupDataSource() {
        File dbFile = new File(dataFolder, configManager.getDatabaseFile());
        
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("jdbc:sqlite:" + dbFile.getAbsolutePath());
        config.setDriverClassName("org.sqlite.JDBC");
        config.setMaximumPoolSize(configManager.getMaximumPoolSize());
        config.setMinimumIdle(configManager.getMinimumIdle());
        config.setConnectionTimeout(configManager.getConnectionTimeout());
        config.setIdleTimeout(600000); // 10分钟
        config.setMaxLifetime(1800000); // 30分钟
        config.setLeakDetectionThreshold(60000); // 1分钟
        
        // SQLite特定配置
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        
        this.dataSource = new HikariDataSource(config);
        
        logger.info("数据库连接池已创建: " + dbFile.getAbsolutePath());
        logger.info("Database connection pool created: " + dbFile.getAbsolutePath());
    }
    
    /**
     * 创建表
     * Create tables
     */
    private void createTables() throws SQLException {
        try (Connection connection = getConnection();
             Statement statement = connection.createStatement()) {
            
            statement.execute(CREATE_TABLE_SQL);
            statement.execute(CREATE_INDEX_SQL);
            
            logger.info("数据库表已创建");
            logger.info("Database tables created");
        }
    }
    
    /**
     * 获取数据库连接
     * Get database connection
     * 
     * @return 数据库连接 Database connection
     * @throws SQLException SQL异常 SQL exception
     */
    @NotNull
    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("数据源未初始化");
        }
        return dataSource.getConnection();
    }
    
    /**
     * 保存皮肤数据
     * Save skin data
     * 
     * @param skinData 皮肤数据 Skin data
     * @return 是否成功 Whether successful
     */
    public boolean saveSkinData(@NotNull SkinData skinData) {
        if (!configManager.isDatabaseCacheEnabled()) {
            return false;
        }
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(INSERT_OR_REPLACE_SQL)) {
            
            statement.setString(1, skinData.getPlayerId().toString());
            statement.setString(2, skinData.getPlayerName());
            statement.setString(3, skinData.getTextureValue());
            statement.setString(4, skinData.getTextureSignature());
            statement.setString(5, skinData.getSkinUrl());
            statement.setString(6, skinData.getCapeUrl());
            statement.setInt(7, skinData.isSlim() ? 1 : 0);
            statement.setLong(8, skinData.getCreatedAt().toEpochMilli());
            statement.setLong(9, skinData.getUpdatedAt().toEpochMilli());
            
            int affected = statement.executeUpdate();
            
            if (configManager.isLogCacheEnabled()) {
                logger.info("保存皮肤数据: " + skinData.getPlayerName() + " (" + skinData.getPlayerId() + ")");
                logger.info("Saved skin data: " + skinData.getPlayerName() + " (" + skinData.getPlayerId() + ")");
            }
            
            return affected > 0;
            
        } catch (SQLException e) {
            logger.warning("保存皮肤数据失败: " + e.getMessage());
            logger.warning("Failed to save skin data: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 根据UUID获取皮肤数据
     * Get skin data by UUID
     * 
     * @param uuid 玩家UUID Player UUID
     * @return 皮肤数据 Skin data
     */
    @Nullable
    public SkinData getSkinDataByUUID(@NotNull UUID uuid) {
        if (!configManager.isDatabaseCacheEnabled()) {
            return null;
        }
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(SELECT_BY_UUID_SQL)) {
            
            statement.setString(1, uuid.toString());
            
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return mapResultSetToSkinData(resultSet);
                }
            }
            
        } catch (SQLException e) {
            logger.warning("获取皮肤数据失败 (UUID): " + e.getMessage());
            logger.warning("Failed to get skin data (UUID): " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 根据用户名获取皮肤数据
     * Get skin data by username
     * 
     * @param username 用户名 Username
     * @return 皮肤数据 Skin data
     */
    @Nullable
    public SkinData getSkinDataByUsername(@NotNull String username) {
        if (!configManager.isDatabaseCacheEnabled()) {
            return null;
        }
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(SELECT_BY_NAME_SQL)) {
            
            statement.setString(1, username);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return mapResultSetToSkinData(resultSet);
                }
            }
            
        } catch (SQLException e) {
            logger.warning("获取皮肤数据失败 (用户名): " + e.getMessage());
            logger.warning("Failed to get skin data (username): " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 删除皮肤数据
     * Delete skin data
     * 
     * @param uuid 玩家UUID Player UUID
     * @return 是否成功 Whether successful
     */
    public boolean deleteSkinData(@NotNull UUID uuid) {
        if (!configManager.isDatabaseCacheEnabled()) {
            return false;
        }
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(DELETE_BY_UUID_SQL)) {
            
            statement.setString(1, uuid.toString());
            int affected = statement.executeUpdate();
            
            if (configManager.isLogCacheEnabled()) {
                logger.info("删除皮肤数据: " + uuid);
                logger.info("Deleted skin data: " + uuid);
            }
            
            return affected > 0;
            
        } catch (SQLException e) {
            logger.warning("删除皮肤数据失败: " + e.getMessage());
            logger.warning("Failed to delete skin data: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 清理过期数据
     * Clean expired data
     * 
     * @return 清理的条目数 Number of cleaned entries
     */
    public int cleanExpiredData() {
        if (!configManager.isDatabaseCacheEnabled()) {
            return 0;
        }
        
        long expirationTime = Instant.now().toEpochMilli() - 
            (configManager.getCacheExpirationHours() * 60 * 60 * 1000L);
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(DELETE_EXPIRED_SQL)) {
            
            statement.setLong(1, expirationTime);
            int affected = statement.executeUpdate();
            
            if (affected > 0) {
                logger.info("清理了 " + affected + " 条过期皮肤数据");
                logger.info("Cleaned " + affected + " expired skin data entries");
            }
            
            return affected;
            
        } catch (SQLException e) {
            logger.warning("清理过期数据失败: " + e.getMessage());
            logger.warning("Failed to clean expired data: " + e.getMessage());
            return 0;
        }
    }
    
    /**
     * 获取缓存条目数
     * Get cache entry count
     * 
     * @return 条目数 Entry count
     */
    public int getCacheSize() {
        if (!configManager.isDatabaseCacheEnabled()) {
            return 0;
        }
        
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(COUNT_ALL_SQL);
             ResultSet resultSet = statement.executeQuery()) {
            
            if (resultSet.next()) {
                return resultSet.getInt(1);
            }
            
        } catch (SQLException e) {
            logger.warning("获取缓存大小失败: " + e.getMessage());
            logger.warning("Failed to get cache size: " + e.getMessage());
        }
        
        return 0;
    }
    
    /**
     * 将ResultSet映射为SkinData
     * Map ResultSet to SkinData
     * 
     * @param resultSet 结果集 Result set
     * @return 皮肤数据 Skin data
     * @throws SQLException SQL异常 SQL exception
     */
    @NotNull
    private SkinData mapResultSetToSkinData(@NotNull ResultSet resultSet) throws SQLException {
        UUID playerId = UUID.fromString(resultSet.getString("player_id"));
        String playerName = resultSet.getString("player_name");
        String textureValue = resultSet.getString("texture_value");
        String textureSignature = resultSet.getString("texture_signature");
        String skinUrl = resultSet.getString("skin_url");
        String capeUrl = resultSet.getString("cape_url");
        boolean isSlim = resultSet.getInt("is_slim") == 1;
        Instant createdAt = Instant.ofEpochMilli(resultSet.getLong("created_at"));
        Instant updatedAt = Instant.ofEpochMilli(resultSet.getLong("updated_at"));
        
        return new SkinData(playerId, playerName, textureValue, textureSignature,
                           skinUrl, capeUrl, isSlim, createdAt, updatedAt);
    }
    
    /**
     * 关闭数据库连接
     * Close database connections
     */
    public void close() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("数据库连接已关闭");
            logger.info("Database connections closed");
        }
    }
}
