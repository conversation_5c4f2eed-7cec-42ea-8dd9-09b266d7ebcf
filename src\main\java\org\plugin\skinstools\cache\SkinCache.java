package org.plugin.skinstools.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.plugin.skinstools.config.ConfigManager;
import org.plugin.skinstools.database.DatabaseManager;
import org.plugin.skinstools.model.SkinData;

import java.time.Duration;
import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.logging.Logger;

/**
 * 皮肤缓存管理器
 * Skin Cache Manager
 * 
 * 提供多层缓存系统：内存缓存 + 数据库缓存
 * Provides multi-tier caching system: memory cache + database cache
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SkinCache {
    
    private final ConfigManager configManager;
    private final DatabaseManager databaseManager;
    private final Logger logger;
    private final Cache<String, SkinData> memoryCache;
    private final Executor cacheExecutor;
    
    public SkinCache(@NotNull ConfigManager configManager, 
                     @NotNull DatabaseManager databaseManager, 
                     @NotNull Logger logger) {
        this.configManager = configManager;
        this.databaseManager = databaseManager;
        this.logger = logger;
        this.cacheExecutor = Executors.newFixedThreadPool(2);
        
        // 初始化内存缓存
        this.memoryCache = Caffeine.newBuilder()
                .maximumSize(configManager.getMemoryCacheSize())
                .expireAfterWrite(Duration.ofHours(configManager.getCacheExpirationHours()))
                .removalListener((key, value, cause) -> {
                    if (configManager.isLogCacheEnabled()) {
                        logger.info("内存缓存移除: " + key + " (原因: " + cause + ")");
                        logger.info("Memory cache removed: " + key + " (cause: " + cause + ")");
                    }
                })
                .build();
        
        logger.info("皮肤缓存系统已初始化");
        logger.info("Skin cache system initialized");
    }
    
    /**
     * 异步获取皮肤数据
     * Asynchronously get skin data
     * 
     * @param uuid 玩家UUID Player UUID
     * @return 皮肤数据的CompletableFuture CompletableFuture of skin data
     */
    @NotNull
    public CompletableFuture<SkinData> getSkinDataAsync(@NotNull UUID uuid) {
        return CompletableFuture.supplyAsync(() -> getSkinData(uuid), cacheExecutor);
    }
    
    /**
     * 异步根据用户名获取皮肤数据
     * Asynchronously get skin data by username
     * 
     * @param username 用户名 Username
     * @return 皮肤数据的CompletableFuture CompletableFuture of skin data
     */
    @NotNull
    public CompletableFuture<SkinData> getSkinDataByUsernameAsync(@NotNull String username) {
        return CompletableFuture.supplyAsync(() -> getSkinDataByUsername(username), cacheExecutor);
    }
    
    /**
     * 获取皮肤数据
     * Get skin data
     * 
     * @param uuid 玩家UUID Player UUID
     * @return 皮肤数据 Skin data
     */
    @Nullable
    public SkinData getSkinData(@NotNull UUID uuid) {
        if (!configManager.isCacheEnabled()) {
            return null;
        }
        
        String cacheKey = "uuid:" + uuid.toString();
        
        // 首先检查内存缓存
        SkinData skinData = memoryCache.getIfPresent(cacheKey);
        if (skinData != null) {
            if (configManager.isLogCacheEnabled()) {
                logger.info("内存缓存命中: " + uuid);
                logger.info("Memory cache hit: " + uuid);
            }
            
            // 检查是否过期
            if (isExpired(skinData)) {
                memoryCache.invalidate(cacheKey);
                if (configManager.isLogCacheEnabled()) {
                    logger.info("内存缓存已过期: " + uuid);
                    logger.info("Memory cache expired: " + uuid);
                }
            } else {
                return skinData;
            }
        }
        
        // 检查数据库缓存
        skinData = databaseManager.getSkinDataByUUID(uuid);
        if (skinData != null) {
            if (configManager.isLogCacheEnabled()) {
                logger.info("数据库缓存命中: " + uuid);
                logger.info("Database cache hit: " + uuid);
            }
            
            // 检查是否过期
            if (isExpired(skinData)) {
                databaseManager.deleteSkinData(uuid);
                if (configManager.isLogCacheEnabled()) {
                    logger.info("数据库缓存已过期: " + uuid);
                    logger.info("Database cache expired: " + uuid);
                }
                return null;
            }
            
            // 将数据放入内存缓存
            memoryCache.put(cacheKey, skinData);
            return skinData;
        }
        
        if (configManager.isLogCacheEnabled()) {
            logger.info("缓存未命中: " + uuid);
            logger.info("Cache miss: " + uuid);
        }
        
        return null;
    }
    
    /**
     * 根据用户名获取皮肤数据
     * Get skin data by username
     * 
     * @param username 用户名 Username
     * @return 皮肤数据 Skin data
     */
    @Nullable
    public SkinData getSkinDataByUsername(@NotNull String username) {
        if (!configManager.isCacheEnabled()) {
            return null;
        }
        
        String cacheKey = "name:" + username.toLowerCase();
        
        // 首先检查内存缓存
        SkinData skinData = memoryCache.getIfPresent(cacheKey);
        if (skinData != null) {
            if (configManager.isLogCacheEnabled()) {
                logger.info("内存缓存命中 (用户名): " + username);
                logger.info("Memory cache hit (username): " + username);
            }
            
            // 检查是否过期
            if (isExpired(skinData)) {
                memoryCache.invalidate(cacheKey);
                if (configManager.isLogCacheEnabled()) {
                    logger.info("内存缓存已过期 (用户名): " + username);
                    logger.info("Memory cache expired (username): " + username);
                }
            } else {
                return skinData;
            }
        }
        
        // 检查数据库缓存
        skinData = databaseManager.getSkinDataByUsername(username);
        if (skinData != null) {
            if (configManager.isLogCacheEnabled()) {
                logger.info("数据库缓存命中 (用户名): " + username);
                logger.info("Database cache hit (username): " + username);
            }
            
            // 检查是否过期
            if (isExpired(skinData)) {
                databaseManager.deleteSkinData(skinData.getPlayerId());
                if (configManager.isLogCacheEnabled()) {
                    logger.info("数据库缓存已过期 (用户名): " + username);
                    logger.info("Database cache expired (username): " + username);
                }
                return null;
            }
            
            // 将数据放入内存缓存
            memoryCache.put(cacheKey, skinData);
            memoryCache.put("uuid:" + skinData.getPlayerId().toString(), skinData);
            return skinData;
        }
        
        if (configManager.isLogCacheEnabled()) {
            logger.info("缓存未命中 (用户名): " + username);
            logger.info("Cache miss (username): " + username);
        }
        
        return null;
    }
    
    /**
     * 异步缓存皮肤数据
     * Asynchronously cache skin data
     * 
     * @param skinData 皮肤数据 Skin data
     * @return CompletableFuture
     */
    @NotNull
    public CompletableFuture<Void> cacheSkinDataAsync(@NotNull SkinData skinData) {
        return CompletableFuture.runAsync(() -> cacheSkinData(skinData), cacheExecutor);
    }
    
    /**
     * 缓存皮肤数据
     * Cache skin data
     * 
     * @param skinData 皮肤数据 Skin data
     */
    public void cacheSkinData(@NotNull SkinData skinData) {
        if (!configManager.isCacheEnabled()) {
            return;
        }
        
        String uuidKey = "uuid:" + skinData.getPlayerId().toString();
        String nameKey = "name:" + skinData.getPlayerName().toLowerCase();
        
        // 更新内存缓存
        memoryCache.put(uuidKey, skinData);
        memoryCache.put(nameKey, skinData);
        
        // 异步更新数据库缓存
        CompletableFuture.runAsync(() -> {
            databaseManager.saveSkinData(skinData);
        }, cacheExecutor);
        
        if (configManager.isLogCacheEnabled()) {
            logger.info("缓存皮肤数据: " + skinData.getPlayerName() + " (" + skinData.getPlayerId() + ")");
            logger.info("Cached skin data: " + skinData.getPlayerName() + " (" + skinData.getPlayerId() + ")");
        }
    }
    
    /**
     * 异步移除缓存
     * Asynchronously remove cache
     * 
     * @param uuid 玩家UUID Player UUID
     * @return CompletableFuture
     */
    @NotNull
    public CompletableFuture<Void> removeCacheAsync(@NotNull UUID uuid) {
        return CompletableFuture.runAsync(() -> removeCache(uuid), cacheExecutor);
    }
    
    /**
     * 移除缓存
     * Remove cache
     * 
     * @param uuid 玩家UUID Player UUID
     */
    public void removeCache(@NotNull UUID uuid) {
        // 首先获取皮肤数据以获取用户名
        SkinData skinData = getSkinData(uuid);
        
        String uuidKey = "uuid:" + uuid.toString();
        memoryCache.invalidate(uuidKey);
        
        if (skinData != null) {
            String nameKey = "name:" + skinData.getPlayerName().toLowerCase();
            memoryCache.invalidate(nameKey);
        }
        
        // 异步从数据库删除
        CompletableFuture.runAsync(() -> {
            databaseManager.deleteSkinData(uuid);
        }, cacheExecutor);
        
        if (configManager.isLogCacheEnabled()) {
            logger.info("移除缓存: " + uuid);
            logger.info("Removed cache: " + uuid);
        }
    }
    
    /**
     * 异步清理所有缓存
     * Asynchronously clear all cache
     * 
     * @return CompletableFuture
     */
    @NotNull
    public CompletableFuture<Void> clearAllCacheAsync() {
        return CompletableFuture.runAsync(this::clearAllCache, cacheExecutor);
    }
    
    /**
     * 清理所有缓存
     * Clear all cache
     */
    public void clearAllCache() {
        memoryCache.invalidateAll();
        
        // 异步清理数据库
        CompletableFuture.runAsync(() -> {
            databaseManager.cleanExpiredData();
        }, cacheExecutor);
        
        logger.info("所有缓存已清理");
        logger.info("All cache cleared");
    }
    
    /**
     * 异步清理过期缓存
     * Asynchronously clean expired cache
     * 
     * @return 清理的条目数 Number of cleaned entries
     */
    @NotNull
    public CompletableFuture<Integer> cleanExpiredCacheAsync() {
        return CompletableFuture.supplyAsync(() -> {
            memoryCache.cleanUp();
            return databaseManager.cleanExpiredData();
        }, cacheExecutor);
    }
    
    /**
     * 获取缓存统计信息
     * Get cache statistics
     * 
     * @return 缓存统计信息 Cache statistics
     */
    @NotNull
    public CacheStats getCacheStats() {
        long memorySize = memoryCache.estimatedSize();
        int databaseSize = databaseManager.getCacheSize();
        
        return new CacheStats(memorySize, databaseSize);
    }
    
    /**
     * 检查皮肤数据是否过期
     * Check if skin data is expired
     * 
     * @param skinData 皮肤数据 Skin data
     * @return 是否过期 Whether expired
     */
    private boolean isExpired(@NotNull SkinData skinData) {
        long expirationTime = skinData.getUpdatedAt().toEpochMilli() + 
            (configManager.getCacheExpirationHours() * 60 * 60 * 1000L);
        return Instant.now().toEpochMilli() > expirationTime;
    }
    
    /**
     * 关闭缓存系统
     * Close cache system
     */
    public void close() {
        if (cacheExecutor instanceof java.util.concurrent.ExecutorService) {
            ((java.util.concurrent.ExecutorService) cacheExecutor).shutdown();
        }
        
        logger.info("皮肤缓存系统已关闭");
        logger.info("Skin cache system closed");
    }
    
    /**
     * 缓存统计信息
     * Cache Statistics
     */
    public static class CacheStats {
        private final long memorySize;
        private final int databaseSize;
        
        public CacheStats(long memorySize, int databaseSize) {
            this.memorySize = memorySize;
            this.databaseSize = databaseSize;
        }
        
        public long getMemorySize() {
            return memorySize;
        }
        
        public int getDatabaseSize() {
            return databaseSize;
        }
        
        public long getTotalSize() {
            return memorySize + databaseSize;
        }
        
        @Override
        public String toString() {
            return "CacheStats{" +
                   "memory=" + memorySize +
                   ", database=" + databaseSize +
                   ", total=" + getTotalSize() +
                   '}';
        }
    }
}
