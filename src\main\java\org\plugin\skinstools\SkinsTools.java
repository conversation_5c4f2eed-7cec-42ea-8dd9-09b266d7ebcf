package org.plugin.skinstools;

import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;
import org.jetbrains.annotations.NotNull;
import org.plugin.skinstools.api.SkinServerClient;
import org.plugin.skinstools.cache.SkinCache;
import org.plugin.skinstools.command.SkinCommand;
import org.plugin.skinstools.command.SkinsToolsCommand;
import org.plugin.skinstools.config.ConfigManager;
import org.plugin.skinstools.database.DatabaseManager;
import org.plugin.skinstools.listener.PlayerListener;
import org.plugin.skinstools.processor.SkinProcessor;

import java.util.logging.Logger;

/**
 * SkinsTools 主插件类
 *
 * 一个用于本地皮肤缓存和管理的Minecraft Paper插件，
 * 支持与Blessing Skin Server集成
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public final class SkinsTools extends JavaPlugin {

    private static SkinsTools instance;
    private Logger logger;

    // 核心组件
    private ConfigManager configManager;
    private DatabaseManager databaseManager;
    private SkinCache skinCache;
    private SkinServerClient skinServerClient;
    private SkinProcessor skinProcessor;

    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();

        logger.info("===================================");
        logger.info("SkinsTools v1.0.0 正在启动...");
        logger.info("作者：NSrank & Augment");
        logger.info("===================================");

        try {
            // 初始化配置管理器
            initializeConfigManager();

            // 初始化数据库
            initializeDatabaseManager();

            // 初始化缓存系统
            initializeCacheSystem();

            // 初始化API客户端
            initializeApiClient();

            // 初始化皮肤处理器
            initializeSkinProcessor();

            // 注册事件监听器
            registerEventListeners();

            // 注册命令
            registerCommands();

            // 启动定期任务
            startScheduledTasks();

            logger.info("SkinsTools 启动完成！");
            logger.info("SkinsTools started successfully!");

        } catch (Exception e) {
            logger.severe("SkinsTools 启动失败: " + e.getMessage());
            logger.severe("SkinsTools failed to start: " + e.getMessage());
            e.printStackTrace();

            // 禁用插件
            Bukkit.getPluginManager().disablePlugin(this);
        }
    }

    @Override
    public void onDisable() {
        logger.info("SkinsTools 正在关闭...");
        logger.info("SkinsTools is shutting down...");

        try {
            // 关闭API客户端
            if (skinServerClient != null) {
                skinServerClient.close();
            }

            // 关闭缓存系统
            if (skinCache != null) {
                skinCache.close();
            }

            // 关闭数据库连接
            if (databaseManager != null) {
                databaseManager.close();
            }

            logger.info("SkinsTools 已关闭！");
            logger.info("SkinsTools has been disabled!");

        } catch (Exception e) {
            logger.warning("关闭插件时发生错误: " + e.getMessage());
            logger.warning("Error occurred while disabling plugin: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 初始化配置管理器
     * Initialize configuration manager
     */
    private void initializeConfigManager() {
        configManager = new ConfigManager(this);
        configManager.loadConfig();

        logger.info("配置管理器已初始化");
        logger.info("Configuration manager initialized");
    }

    /**
     * 初始化数据库管理器
     * Initialize database manager
     */
    private void initializeDatabaseManager() {
        databaseManager = new DatabaseManager(configManager, logger, getDataFolder());
        databaseManager.initialize();

        logger.info("数据库管理器已初始化");
        logger.info("Database manager initialized");
    }

    /**
     * 初始化缓存系统
     * Initialize cache system
     */
    private void initializeCacheSystem() {
        skinCache = new SkinCache(configManager, databaseManager, logger);

        logger.info("缓存系统已初始化");
        logger.info("Cache system initialized");
    }

    /**
     * 初始化API客户端
     * Initialize API client
     */
    private void initializeApiClient() {
        skinServerClient = new SkinServerClient(configManager, logger);

        logger.info("API客户端已初始化");
        logger.info("API client initialized");
    }

    /**
     * 初始化皮肤处理器
     * Initialize skin processor
     */
    private void initializeSkinProcessor() {
        skinProcessor = new SkinProcessor(configManager, skinServerClient, skinCache, logger);

        logger.info("皮肤处理器已初始化");
        logger.info("Skin processor initialized");
    }

    /**
     * 注册事件监听器
     * Register event listeners
     */
    private void registerEventListeners() {
        PlayerListener playerListener = new PlayerListener(this, configManager, skinProcessor);
        Bukkit.getPluginManager().registerEvents(playerListener, this);

        logger.info("事件监听器已注册");
        logger.info("Event listeners registered");
    }

    /**
     * 注册命令
     * Register commands
     */
    private void registerCommands() {
        // 注册主命令
        SkinsToolsCommand skinsToolsCommand = new SkinsToolsCommand(this, configManager, skinProcessor, skinCache);
        getCommand("skinstools").setExecutor(skinsToolsCommand);
        getCommand("skinstools").setTabCompleter(skinsToolsCommand);

        // 注册皮肤命令
        SkinCommand skinCommand = new SkinCommand(this, configManager, skinProcessor);
        getCommand("skin").setExecutor(skinCommand);
        getCommand("skin").setTabCompleter(skinCommand);

        logger.info("命令已注册");
        logger.info("Commands registered");
    }

    /**
     * 启动定期任务
     * Start scheduled tasks
     */
    private void startScheduledTasks() {
        // 定期清理过期缓存 (每小时执行一次)
        Bukkit.getScheduler().runTaskTimerAsynchronously(this, () -> {
            try {
                int cleaned = skinCache.cleanExpiredCacheAsync().get();
                if (cleaned > 0 && configManager.isDebugEnabled()) {
                    logger.info("定期清理了 " + cleaned + " 条过期缓存");
                    logger.info("Periodically cleaned " + cleaned + " expired cache entries");
                }
            } catch (Exception e) {
                logger.warning("定期清理缓存时发生错误: " + e.getMessage());
                logger.warning("Error occurred during periodic cache cleanup: " + e.getMessage());
            }
        }, 72000L, 72000L); // 1小时 = 72000 ticks

        logger.info("定期任务已启动");
        logger.info("Scheduled tasks started");
    }

    /**
     * 获取插件实例
     * Get plugin instance
     *
     * @return 插件实例 Plugin instance
     */
    @NotNull
    public static SkinsTools getInstance() {
        return instance;
    }

    /**
     * 获取插件日志记录器
     * Get plugin logger
     *
     * @return 日志记录器 Logger
     */
    @NotNull
    public Logger getPluginLogger() {
        return logger;
    }

    /**
     * 获取配置管理器
     * Get configuration manager
     *
     * @return 配置管理器 Configuration manager
     */
    @NotNull
    public ConfigManager getConfigManager() {
        return configManager;
    }

    /**
     * 获取皮肤处理器
     * Get skin processor
     *
     * @return 皮肤处理器 Skin processor
     */
    @NotNull
    public SkinProcessor getSkinProcessor() {
        return skinProcessor;
    }

    /**
     * 获取皮肤缓存
     * Get skin cache
     *
     * @return 皮肤缓存 Skin cache
     */
    @NotNull
    public SkinCache getSkinCache() {
        return skinCache;
    }
}
